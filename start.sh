#!/bin/bash

# Perplexity AI Agent Startup Script
# This script starts the entire Perplexity AI Agent system

set -e

echo "🚀 Starting Perplexity AI Agent..."
echo "=================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ Created .env file. You can customize it if needed."
fi

# Create logs directory
mkdir -p logs

echo "🐳 Starting Docker containers..."
docker-compose up -d

echo "⏳ Waiting for services to start..."
sleep 10

# Check service health
echo "🔍 Checking service health..."

# Check Redis
if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis is running"
else
    echo "❌ Redis is not responding"
fi

# Check Selenium Hub
if curl -s http://localhost:4444/wd/hub/status > /dev/null 2>&1; then
    echo "✅ Selenium Hub is running"
else
    echo "❌ Selenium Hub is not responding"
fi

# Check main application
echo "⏳ Waiting for main application to start..."
sleep 15

if curl -s http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ Perplexity AI Agent is running"
else
    echo "❌ Perplexity AI Agent is not responding"
    echo "📋 Checking logs..."
    docker-compose logs --tail=20 perplexity-agent
fi

echo ""
echo "🎉 Perplexity AI Agent is ready!"
echo "================================"
echo ""
echo "📊 Service URLs:"
echo "   • Main API: http://localhost:8000"
echo "   • API Docs: http://localhost:8000/docs"
echo "   • Debug Interface: http://localhost:8000/debug"
echo "   • Browser Viewer: http://localhost:7900"
echo ""
echo "🧪 Quick Test:"
echo "   python test_agent.py"
echo ""
echo "📝 Example API Call:"
echo '   curl -X POST http://localhost:8000/query \'
echo '     -H "Content-Type: application/json" \'
echo '     -d '"'"'{"query": "What is the weather like today?"}'"'"
echo ""
echo "🔧 Management Commands:"
echo "   • View logs: docker-compose logs -f"
echo "   • Stop services: docker-compose down"
echo "   • Restart: docker-compose restart"
echo ""
echo "🐛 Debugging:"
echo "   • Visit http://localhost:7900 to watch browser automation in your web browser"
echo "   • Check service status: docker-compose ps"
echo "   • View application logs: docker-compose logs perplexity-agent"
echo ""

# Optionally run the test script
read -p "🧪 Would you like to run the test script now? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧪 Running test script..."
    python test_agent.py
fi

echo "✨ Setup complete! Happy automating! ✨"
