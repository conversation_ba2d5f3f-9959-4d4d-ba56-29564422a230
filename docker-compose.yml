version: '3.8'

services:
  redis:
    image: redis:6.2-alpine
    container_name: perplexity-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: unless-stopped

  selenium-hub:
    image: selenium/hub:4.15.0
    container_name: perplexity-selenium-hub
    ports:
      - "4444:4444"
    environment:
      - GRID_MAX_SESSION=10
      - GRID_BROWSER_TIMEOUT=300
      - GRID_TIMEOUT=300
    restart: unless-stopped

  chrome-debug:
    image: selenium/node-chrome-debug:4.15.0
    container_name: perplexity-chrome-debug
    ports:
      - "5900:5900"  # VNC port for debugging
    environment:
      - HUB_HOST=selenium-hub
      - HUB_PORT=4444
      - NODE_MAX_INSTANCES=5
      - NODE_MAX_SESSION=5
    depends_on:
      - selenium-hub
    volumes:
      - /dev/shm:/dev/shm
    restart: unless-stopped

  perplexity-agent:
    build: .
    container_name: perplexity-agent
    ports:
      - "8000:8000"
    environment:
      - REDIS_URL=redis://redis:6379
      - SELENIUM_HUB_URL=http://selenium-hub:4444/wd/hub
      - DEBUG_MODE=true
      - LOG_LEVEL=INFO
    depends_on:
      - redis
      - selenium-hub
      - chrome-debug
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

volumes:
  redis_data:
