services:
  redis:
    image: redis:7-alpine
    container_name: perplexity-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: unless-stopped

  chrome-standalone:
    image: seleniarm/standalone-chromium:latest
    container_name: perplexity-chrome-standalone
    ports:
      - "4444:4444"  # Selenium WebDriver port
      - "7900:7900"  # noVNC web interface
    environment:
      - SE_ENABLE_VNC=true
      - SE_VNC_NO_PASSWORD=1
      - SE_SCREEN_WIDTH=1920
      - SE_SCREEN_HEIGHT=1080
      - SE_NODE_MAX_INSTANCES=5
      - SE_NODE_MAX_SESSIONS=5
    volumes:
      - /dev/shm:/dev/shm
    restart: unless-stopped

  perplexity-agent:
    build: .
    container_name: perplexity-agent
    ports:
      - "8000:8000"
    environment:
      - REDIS_URL=redis://redis:6379
      - SELENIUM_HUB_URL=http://chrome-standalone:4444/wd/hub
      - DEBUG_MODE=true
      - LOG_LEVEL=INFO
    depends_on:
      - redis
      - chrome-standalone
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

volumes:
  redis_data:
