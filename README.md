# Perplexity AI Agent

A web automation agent for Perplexity AI queries with Deep Research support, built with Python, Selenium, Redis, and Docker.

## 🚀 Features

- **Deep Research Support** - Handles Perplexity's comprehensive research mode (up to 10 minutes)
- **Custom Sources** - Configure specific web sources for targeted searches
- **Concurrent Processing** - Multiple browser tabs for handling concurrent requests
- **Visual Debugging** - noVNC web access for real-time browser automation monitoring
- **Job Queue System** - Redis-based queue with retry logic and status tracking
- **REST API** - FastAPI-based API for easy integration
- **Docker Deployment** - Complete containerized setup with Docker Compose
- **Anti-Detection Technology** - Advanced stealth mode to avoid bot detection
- **Authentication Support** - Login to your Perplexity account for enhanced features
- **Human Behavior Simulation** - Random delays, mouse movements, and typing patterns
- **Extended Timeouts** - Configurable timeouts up to 10 minutes for complex queries

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FastAPI       │    │   Redis         │    │   Selenium      │
│   Web Server    │◄──►│   Job Queue     │◄──►│   Browser       │
│                 │    │                 │    │   Automation    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Client    │    │   Job Storage   │    │   VNC Debug     │
│   Integration   │    │   & Status      │    │   Viewer        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🛠️ Technology Stack

- **Python 3.11** - Core application language
- **FastAPI** - Modern web framework for APIs
- **Selenium WebDriver** - Browser automation
- **Redis** - Job queue and temporary storage
- **Docker & Docker Compose** - Containerization
- **Chrome Debug** - Browser with VNC support for debugging

## 📦 Quick Start

### Prerequisites

- Docker and Docker Compose
- VNC client (for debugging)

### 1. Clone and Setup

```bash
# Clone the repository
git clone <repository-url>
cd perplexity-ai-agent

# Copy environment configuration
cp .env.example .env

# (Optional) Configure Perplexity authentication
# Edit .env file and set:
# ENABLE_PERPLEXITY_AUTH=true
# PERPLEXITY_EMAIL=<EMAIL>
# PERPLEXITY_PASSWORD=your-password

# Start all services
docker-compose up -d --build

# Check service status
docker-compose ps
```

### 2. Submit Your First Query

```bash
# Quick search
curl -X POST http://localhost:8000/query \
  -H "Content-Type: application/json" \
  -d '{"query": "What is the weather like today?"}'

# Deep research with custom sources
curl -X POST http://localhost:8000/query \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Latest developments in artificial intelligence",
    "options": {
      "mode": "deep_research",
      "sources": ["arxiv.org", "openai.com", "anthropic.com"]
    }
  }'
```

### 3. Get Results

```bash
# Get job result (replace JOB_ID with actual ID from step 2)
curl http://localhost:8000/result/JOB_ID

# Check job status
curl http://localhost:8000/status/JOB_ID
```

### 4. Debug with noVNC

1. Visit http://localhost:7900 for direct browser access
2. Or visit http://localhost:8000/debug for integrated debugging interface
3. Watch browser automation in real-time (no additional software required)

## 🔧 API Reference

### Submit Query
```http
POST /query
Content-Type: application/json

{
  "query": "Your search query here",
  "options": {
    "mode": "quick_search" | "deep_research",
    "sources": ["domain1.com", "domain2.com"],
    "priority": "normal" | "high"
  }
}
```

**Response:**
```json
{
  "job_id": "uuid-string",
  "status": "submitted",
  "message": "Query submitted successfully"
}
```

### Get Result
```http
GET /result/{job_id}
```

**Response:**
```json
{
  "job_id": "uuid-string",
  "status": "completed",
  "query": "Original query",
  "result": {
    "answer": "Comprehensive answer from Perplexity",
    "sources": [
      {"title": "Source Title", "url": "https://example.com"}
    ],
    "mode": "deep_research",
    "timestamp": "2025-06-20T11:05:00Z"
  },
  "created_at": "2025-06-20T11:00:00Z",
  "updated_at": "2025-06-20T11:05:00Z",
  "attempts": 1
}
```

### Other Endpoints

- `GET /status/{job_id}` - Get job status only
- `DELETE /cancel/{job_id}` - Cancel a pending/processing job
- `GET /stats` - Get system statistics
- `GET /health` - Health check
- `GET /debug` - VNC debugging interface
- `GET /docs` - Interactive API documentation

## ⚙️ Configuration

### Environment Variables

```bash
# Redis Configuration
REDIS_URL=redis://redis:6379

# Selenium Configuration  
SELENIUM_HUB_URL=http://selenium-hub:4444/wd/hub

# Application Configuration
DEBUG_MODE=true
LOG_LEVEL=INFO
```

### Query Options

- **mode**: `"quick_search"` (default) or `"deep_research"`
- **sources**: Array of domain names for custom source filtering
- **priority**: `"normal"` (default) or `"high"`

### Timeouts

- Quick Search: 600 seconds (10 minutes, configurable)
- Deep Research: 600 seconds (10 minutes, configurable)
- Page Load: 60 seconds
- Page Interactions: 30 seconds

## 🔐 Authentication Setup

The agent supports logging into your Perplexity account using email-based verification for enhanced features and higher rate limits.

### 1. Configure Email Authentication

Edit your `.env` file:

```bash
# Enable authentication
ENABLE_PERPLEXITY_AUTH=true

# Your Perplexity email (no password needed)
PERPLEXITY_EMAIL=<EMAIL>

# Enable manual verification code entry
MANUAL_EMAIL_VERIFICATION=true
```

### 2. Email Verification Process

Perplexity uses email-based authentication without passwords:

1. **Email Submission**: The agent enters your email address
2. **Verification Code**: Perplexity sends a verification code to your email
3. **Code Entry**: You enter the code via noVNC interface or API endpoint
4. **Session Persistence**: The agent saves the authenticated session

### 3. Manual Verification Methods

**Option A: noVNC Interface (Recommended)**
1. Visit http://localhost:7900 in your browser
2. Watch the authentication process in real-time
3. Enter the verification code when prompted
4. The agent will detect and submit the code automatically

**Option B: API Endpoint**
```bash
# Submit verification code via API
curl -X POST http://localhost:8000/auth/verify-code \
  -H "Content-Type: application/json" \
  -d '{"code": "123456"}'

# Check authentication status
curl http://localhost:8000/auth/status
```

### 4. Authentication Features

- **Email-Based Login**: Uses Perplexity's actual OAuth/email verification flow
- **Session Management**: Maintains login state between restarts
- **Re-authentication**: Automatically re-authenticates if session expires
- **Secure Storage**: Session data is stored locally with encryption
- **Manual Intervention**: Supports manual code entry when needed
- **Real-time Monitoring**: Watch the process via noVNC interface

### 5. Troubleshooting Authentication

If authentication fails:

1. **Check Email**: Verify the email address is correct and accessible
2. **Check Spam**: Look for verification emails in spam/junk folders
3. **Timeout Issues**: The system waits 5 minutes for code entry
4. **Manual Completion**: Use noVNC at http://localhost:7900 to complete manually
5. **API Method**: Use the `/auth/verify-code` endpoint as an alternative

### 6. Authentication Flow Example

```bash
# 1. Start the system with authentication enabled
docker-compose up -d

# 2. The agent will automatically attempt login
# 3. Check your email for verification code
# 4. Enter code via noVNC or API:

curl -X POST http://localhost:8000/auth/verify-code \
  -H "Content-Type: application/json" \
  -d '{"code": "YOUR_CODE_HERE"}'

# 5. Verify authentication status
curl http://localhost:8000/auth/status
```

## 🥷 Anti-Detection Features

The agent includes advanced anti-detection technology to avoid being blocked by Perplexity's bot detection systems.

### Stealth Mode Features

- **Browser Fingerprinting Protection**: Masks automation indicators
- **Realistic User Agents**: Rotates between realistic browser user agents
- **Human Behavior Simulation**: Random delays, mouse movements, and typing patterns
- **WebDriver Detection Bypass**: Removes automation flags from the browser
- **WebRTC Blocking**: Prevents IP leaks through WebRTC
- **Custom Viewport Sizes**: Randomizes window dimensions

### Configuration Options

```bash
# Enable/disable stealth features in .env
ENABLE_STEALTH_MODE=true
RANDOM_USER_AGENT=true
SIMULATE_HUMAN_BEHAVIOR=true
RANDOM_DELAYS_MIN=1.0
RANDOM_DELAYS_MAX=3.0
MOUSE_MOVEMENT_SIMULATION=true
DISABLE_WEBRTC=true
```

### Human Behavior Simulation

- **Typing Patterns**: Realistic keystroke timing and occasional pauses
- **Mouse Movements**: Natural cursor movements before clicking
- **Reading Delays**: Simulates time spent reading content
- **Random Delays**: Variable delays between actions (1-3 seconds by default)

### Browser Fingerprinting Countermeasures

- **User Agent Rotation**: Uses fake-useragent library for realistic UAs
- **Viewport Randomization**: Slightly randomizes window size
- **Plugin/Extension Masking**: Disables detectable browser features
- **WebGL Fingerprinting**: Masks GPU and rendering details

## 🛡️ Cloudflare Bypass Technology

The agent includes advanced Cloudflare bypass capabilities to handle Perplexity's protection systems.

### Cloudflare Detection & Bypass

- **Automatic Detection**: Identifies Cloudflare challenges and protection screens
- **Challenge Waiting**: Waits for JavaScript challenges to complete automatically
- **Multiple Bypass Methods**: Uses undetected-chromedriver, SeleniumBase, and enhanced stealth
- **Real-time Monitoring**: Tracks bypass progress and completion

### Bypass Configuration

```bash
# Enable Cloudflare bypass features
CLOUDFLARE_BYPASS_ENABLED=true
WAIT_FOR_CLOUDFLARE=true
CLOUDFLARE_TIMEOUT=60

# Choose bypass method
USE_UNDETECTED_CHROME=true    # Recommended for Cloudflare
USE_SELENIUMBASE=false        # Alternative method
```

### Advanced Anti-Detection

- **Undetected ChromeDriver**: Uses community-patched WebDriver
- **Enhanced Stealth Mode**: Multiple layers of automation hiding
- **TLS Fingerprinting**: Mimics real browser TLS handshakes
- **HTTP2 Support**: Uses modern HTTP protocols
- **JavaScript Execution**: Handles dynamic challenges automatically

### Troubleshooting Cloudflare Issues

If queries fail due to Cloudflare:

1. **Enable All Bypass Features**: Set all Cloudflare options to `true`
2. **Use noVNC Monitoring**: Watch the bypass process at http://localhost:7900
3. **Increase Timeouts**: Extend `CLOUDFLARE_TIMEOUT` if challenges take longer
4. **Try Different Methods**: Switch between undetected-chrome and seleniumbase
5. **Manual Intervention**: Complete challenges manually via noVNC if needed

## 🐛 Debugging

### noVNC Web Access

1. **Direct Access**: Visit `http://localhost:7900` in any web browser
2. **Integrated Interface**: Visit `http://localhost:8000/debug`
3. **No Installation Required**: Works in Chrome, Firefox, Safari, Edge

### Logs

```bash
# View application logs
docker-compose logs perplexity-agent

# View all service logs
docker-compose logs

# Follow logs in real-time
docker-compose logs -f perplexity-agent
```

### Common Issues

1. **Connection Refused**: Ensure all services are running with `docker-compose ps`
2. **Selenium Timeout**: Check if Chrome debug container is healthy
3. **Redis Connection**: Verify Redis container is accessible
4. **noVNC Not Working**: Ensure port 7900 is not blocked by firewall

## 📊 Monitoring

### System Statistics

Visit `http://localhost:8000/stats` or `http://localhost:8000/debug` for:

- Job queue statistics (pending, processing, completed, failed)
- Active browser tabs count
- System health status
- Performance metrics

### Health Check

```bash
curl http://localhost:8000/health
```

## 🔄 Development

### Local Development

```bash
# Install dependencies
pip install -r requirements.txt

# Set environment variables
export REDIS_URL=redis://localhost:6379
export SELENIUM_HUB_URL=http://localhost:4444/wd/hub

# Run the application
python -m uvicorn src.api_server:app --reload --host 0.0.0.0 --port 8000
```

### Testing

```bash
# Run a test query
python -c "
import requests
response = requests.post('http://localhost:8000/query', 
    json={'query': 'Test query'})
print(response.json())
"
```

## 🚦 Production Deployment

### Scaling

To handle more concurrent requests:

1. **Increase browser nodes**:
```yaml
# In docker-compose.yml
chrome-debug:
  scale: 3  # Run 3 Chrome instances
```

2. **Adjust concurrent job limits**:
```bash
# Set environment variable
MAX_CONCURRENT_JOBS=20
```

### Security

- Use Redis password in production
- Configure firewall rules for VNC port
- Use HTTPS with reverse proxy
- Implement API authentication

### Monitoring

- Set up log aggregation (ELK stack)
- Configure health check monitoring
- Set up alerts for failed jobs

## 📝 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For issues and questions:

1. Check the debugging section above
2. Review Docker Compose logs
3. Use VNC debugging to observe browser behavior
4. Open an issue with detailed error information
