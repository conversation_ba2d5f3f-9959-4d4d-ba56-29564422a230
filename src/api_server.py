import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any, Optional, List
from datetime import datetime

from fastapi import Fast<PERSON><PERSON>, HTTPException, BackgroundTasks
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field
import uvicorn

from .config import settings
from .redis_client import redis_client, JobStatus
from .perplexity_handler import perplexity_handler

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


# Pydantic models
class QueryRequest(BaseModel):
    query: str = Field(..., min_length=1, max_length=2000, description="The query to search for")
    options: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Query options")
    
    class Config:
        schema_extra = {
            "example": {
                "query": "What are the latest developments in AI?",
                "options": {
                    "mode": "deep_research",
                    "sources": ["arxiv.org", "openai.com"],
                    "priority": "normal"
                }
            }
        }


class QueryResponse(BaseModel):
    job_id: str
    status: str
    message: str


class JobResult(BaseModel):
    job_id: str
    status: str
    query: str
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    created_at: str
    updated_at: str
    attempts: int


class StatsResponse(BaseModel):
    job_stats: Dict[str, int]
    active_tabs: int
    is_running: bool
    max_concurrent_jobs: int
    timestamp: str


# Lifespan context manager
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan"""
    # Startup
    logger.info("Starting Perplexity AI Agent...")
    
    try:
        # Connect to Redis
        await redis_client.connect()
        
        # Start Perplexity handler
        await perplexity_handler.start()
        
        # Start cleanup task
        cleanup_task = asyncio.create_task(periodic_cleanup())
        
        logger.info("Perplexity AI Agent started successfully")
        
        yield
        
    finally:
        # Shutdown
        logger.info("Shutting down Perplexity AI Agent...")
        
        # Cancel cleanup task
        cleanup_task.cancel()
        try:
            await cleanup_task
        except asyncio.CancelledError:
            pass
        
        # Stop handler
        await perplexity_handler.stop()
        
        # Disconnect from Redis
        await redis_client.disconnect()
        
        logger.info("Perplexity AI Agent shut down complete")


# Create FastAPI app
app = FastAPI(
    title=settings.api_title,
    description=settings.api_description,
    version=settings.api_version,
    debug=settings.debug_mode,
    lifespan=lifespan
)

# Mount static files
try:
    app.mount("/static", StaticFiles(directory="static"), name="static")
except Exception as e:
    logger.warning(f"Could not mount static files: {e}")


# Background task for periodic cleanup
async def periodic_cleanup():
    """Periodic cleanup of expired jobs"""
    while True:
        try:
            await redis_client.cleanup_expired_jobs()
            await asyncio.sleep(300)  # Run every 5 minutes
        except asyncio.CancelledError:
            break
        except Exception as e:
            logger.error(f"Error in periodic cleanup: {e}")
            await asyncio.sleep(60)


# API Routes
@app.get("/", response_class=HTMLResponse)
async def root():
    """Root endpoint with API documentation"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Perplexity AI Agent</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 800px; margin: 0 auto; }
            .endpoint { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
            .method { color: #007bff; font-weight: bold; }
            pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Perplexity AI Agent</h1>
            <p>Web automation agent for Perplexity AI queries with Deep Research support.</p>
            
            <h2>API Endpoints</h2>
            
            <div class="endpoint">
                <h3><span class="method">POST</span> /query</h3>
                <p>Submit a new query for processing</p>
                <pre>curl -X POST http://localhost:8000/query \\
  -H "Content-Type: application/json" \\
  -d '{"query": "What is the weather like today?"}'</pre>
            </div>
            
            <div class="endpoint">
                <h3><span class="method">GET</span> /result/{job_id}</h3>
                <p>Get the result of a query</p>
            </div>
            
            <div class="endpoint">
                <h3><span class="method">GET</span> /status/{job_id}</h3>
                <p>Get the status of a query</p>
            </div>
            
            <div class="endpoint">
                <h3><span class="method">GET</span> /stats</h3>
                <p>Get system statistics</p>
            </div>
            
            <div class="endpoint">
                <h3><span class="method">GET</span> /debug</h3>
                <p>Access VNC viewer for browser debugging</p>
            </div>
            
            <h2>Documentation</h2>
            <p><a href="/docs">Interactive API Documentation (Swagger UI)</a></p>
            <p><a href="/redoc">Alternative API Documentation (ReDoc)</a></p>
        </div>
    </body>
    </html>
    """


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check Redis connection
        await redis_client.redis_client.ping()
        
        # Check handler status
        is_running = perplexity_handler.is_running
        
        return {
            "status": "healthy" if is_running else "degraded",
            "redis": "connected",
            "handler": "running" if is_running else "stopped",
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
        )


@app.post("/query", response_model=QueryResponse)
async def submit_query(request: QueryRequest):
    """Submit a new query for processing"""
    try:
        job_id = await perplexity_handler.submit_query(
            request.query, 
            request.options
        )
        
        return QueryResponse(
            job_id=job_id,
            status="submitted",
            message="Query submitted successfully"
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error submitting query: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/result/{job_id}", response_model=JobResult)
async def get_result(job_id: str):
    """Get the result of a query"""
    try:
        job_data = await perplexity_handler.get_query_result(job_id)
        
        if not job_data:
            raise HTTPException(status_code=404, detail="Job not found")
        
        return JobResult(**job_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting result for job {job_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/status/{job_id}")
async def get_status(job_id: str):
    """Get the status of a query"""
    try:
        job_data = await perplexity_handler.get_query_result(job_id)
        
        if not job_data:
            raise HTTPException(status_code=404, detail="Job not found")
        
        return {
            "job_id": job_id,
            "status": job_data.get("status"),
            "created_at": job_data.get("created_at"),
            "updated_at": job_data.get("updated_at"),
            "attempts": job_data.get("attempts", 0)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting status for job {job_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.delete("/cancel/{job_id}")
async def cancel_query(job_id: str):
    """Cancel a pending or processing query"""
    try:
        success = await perplexity_handler.cancel_query(job_id)
        
        if not success:
            raise HTTPException(status_code=400, detail="Cannot cancel job")
        
        return {"message": "Job cancelled successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling job {job_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/stats", response_model=StatsResponse)
async def get_stats():
    """Get system statistics"""
    try:
        stats = await perplexity_handler.get_stats()
        return StatsResponse(**stats)
        
    except Exception as e:
        logger.error(f"Error getting stats: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/debug", response_class=HTMLResponse)
async def debug_viewer():
    """VNC viewer for debugging browser automation"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Browser Debug Viewer</title>
        <style>
            body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
            .container { max-width: 1200px; margin: 0 auto; }
            .info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
            iframe { width: 100%; height: 600px; border: 1px solid #ccc; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Browser Debug Viewer</h1>
            <div class="info">
                <p><strong>VNC Connection:</strong> localhost:5900</p>
                <p><strong>Instructions:</strong> Use a VNC client to connect to localhost:5900 to view the browser automation in real-time.</p>
                <p><strong>Recommended VNC Clients:</strong></p>
                <ul>
                    <li>macOS: Screen Sharing (built-in) or VNC Viewer</li>
                    <li>Windows: TightVNC, RealVNC, or VNC Viewer</li>
                    <li>Linux: Remmina, Vinagre, or vncviewer</li>
                </ul>
            </div>
            
            <h2>Quick Connect</h2>
            <p>On macOS, you can connect directly by opening Safari and going to:</p>
            <pre>vnc://localhost:5900</pre>
            
            <h2>Current Stats</h2>
            <div id="stats">Loading...</div>
        </div>
        
        <script>
            async function loadStats() {
                try {
                    const response = await fetch('/stats');
                    const stats = await response.json();
                    document.getElementById('stats').innerHTML = `
                        <pre>${JSON.stringify(stats, null, 2)}</pre>
                    `;
                } catch (error) {
                    document.getElementById('stats').innerHTML = 'Error loading stats';
                }
            }
            
            loadStats();
            setInterval(loadStats, 5000); // Refresh every 5 seconds
        </script>
    </body>
    </html>
    """


if __name__ == "__main__":
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug_mode,
        log_level=settings.log_level.lower()
    )
