import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from .browser_agent import Browser<PERSON>gent
from .redis_client import redis_client, JobStatus
from .config import settings

logger = logging.getLogger(__name__)


class PerplexityHandler:
    def __init__(self):
        self.browser_agent = None
        self.is_running = False
        self.worker_task = None
    
    async def start(self):
        """Start the Perplexity handler"""
        try:
            # Initialize browser agent
            self.browser_agent = BrowserAgent()
            await self.browser_agent.start()
            
            # Start worker task
            self.is_running = True
            self.worker_task = asyncio.create_task(self._worker_loop())
            
            logger.info("Perplexity handler started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start Perplexity handler: {e}")
            raise
    
    async def stop(self):
        """Stop the Perplexity handler"""
        self.is_running = False
        
        if self.worker_task:
            self.worker_task.cancel()
            try:
                await self.worker_task
            except asyncio.CancelledError:
                pass
        
        if self.browser_agent:
            await self.browser_agent.stop()
        
        logger.info("Perplexity handler stopped")
    
    async def _worker_loop(self):
        """Main worker loop to process jobs"""
        logger.info("Worker loop started")
        
        while self.is_running:
            try:
                # Get next job from queue
                job_id = await redis_client.get_next_job()
                
                if job_id:
                    logger.info(f"Processing job: {job_id}")
                    
                    # Check if we have too many concurrent jobs
                    if len(self.browser_agent.current_tab_handles) >= settings.max_concurrent_jobs:
                        logger.warning("Max concurrent jobs reached, skipping job")
                        # Put job back in queue
                        await redis_client.redis_client.lpush("job_queue:pending", job_id)
                        await asyncio.sleep(5)
                        continue
                    
                    # Process the job
                    success = await self.browser_agent.process_job(job_id)
                    
                    if not success:
                        # Handle retry logic
                        await self._handle_job_retry(job_id)
                
                else:
                    # No jobs available, wait a bit
                    await asyncio.sleep(1)
                
            except asyncio.CancelledError:
                logger.info("Worker loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in worker loop: {e}")
                await asyncio.sleep(5)
    
    async def _handle_job_retry(self, job_id: str):
        """Handle job retry logic"""
        try:
            job_data = await redis_client.get_job(job_id)
            if not job_data:
                return
            
            attempts = await redis_client.increment_job_attempts(job_id)
            max_attempts = int(job_data.get("max_attempts", settings.job_retry_attempts))

            if attempts < max_attempts:
                logger.info(f"Retrying job {job_id} (attempt {attempts}/{max_attempts})")
                
                # Wait before retry
                await asyncio.sleep(settings.job_retry_delay)
                
                # Put job back in queue
                await redis_client.redis_client.lpush("job_queue:pending", job_id)
                
                # Reset status to pending
                await redis_client.update_job_status(job_id, JobStatus.PENDING)
            else:
                logger.error(f"Job {job_id} failed after {attempts} attempts")
                await redis_client.update_job_status(
                    job_id, 
                    JobStatus.FAILED, 
                    error=f"Failed after {attempts} attempts"
                )
        
        except Exception as e:
            logger.error(f"Error handling job retry for {job_id}: {e}")
    
    async def submit_query(self, query: str, options: Dict[str, Any] = None) -> str:
        """Submit a new query for processing"""
        if options is None:
            options = {}
        
        # Validate query
        if not query or not query.strip():
            raise ValueError("Query cannot be empty")
        
        # Set default options
        default_options = {
            "mode": "quick_search",
            "sources": [],
            "priority": "normal"
        }
        default_options.update(options)
        
        # Create job
        job_id = await redis_client.create_job(query.strip(), default_options)
        
        logger.info(f"Submitted query job {job_id}: {query[:100]}...")
        return job_id
    
    async def get_query_result(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get the result of a query"""
        return await redis_client.get_job(job_id)
    
    async def cancel_query(self, job_id: str) -> bool:
        """Cancel a pending or processing query"""
        try:
            job_data = await redis_client.get_job(job_id)
            if not job_data:
                return False
            
            status = job_data.get("status")
            
            if status in [JobStatus.PENDING, JobStatus.PROCESSING]:
                await redis_client.update_job_status(job_id, JobStatus.CANCELLED)
                
                # If processing, close the tab
                if job_id in self.browser_agent.current_tab_handles:
                    self.browser_agent._close_tab(job_id)
                
                logger.info(f"Cancelled job {job_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error cancelling job {job_id}: {e}")
            return False
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get handler statistics"""
        try:
            job_stats = await redis_client.get_job_stats()
            
            return {
                "job_stats": job_stats,
                "active_tabs": len(self.browser_agent.current_tab_handles) if self.browser_agent else 0,
                "is_running": self.is_running,
                "max_concurrent_jobs": settings.max_concurrent_jobs,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting stats: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }


# Global handler instance
perplexity_handler = PerplexityHandler()
