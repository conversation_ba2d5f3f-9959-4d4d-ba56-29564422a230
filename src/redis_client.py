import json
import uuid
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import redis.asyncio as redis
from enum import Enum
import logging

from .config import settings

logger = logging.getLogger(__name__)


class JobStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class RedisClient:
    def __init__(self):
        self.redis_pool = None
        self.redis_client = None
    
    async def connect(self):
        """Initialize Redis connection pool"""
        try:
            self.redis_pool = redis.ConnectionPool.from_url(
                settings.redis_url,
                db=settings.redis_db,
                password=settings.redis_password,
                decode_responses=True,
                max_connections=20
            )
            self.redis_client = redis.Redis(connection_pool=self.redis_pool)
            
            # Test connection
            await self.redis_client.ping()
            logger.info("Redis connection established successfully")
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    async def disconnect(self):
        """Close Redis connection"""
        if self.redis_client:
            await self.redis_client.close()
        if self.redis_pool:
            await self.redis_pool.disconnect()
    
    async def create_job(self, query: str, options: Dict[str, Any]) -> str:
        """Create a new job and add it to the queue"""
        job_id = str(uuid.uuid4())
        
        job_data = {
            "job_id": job_id,
            "query": query,
            "options": options,
            "status": JobStatus.PENDING,
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat(),
            "attempts": 0,
            "max_attempts": settings.job_retry_attempts
        }
        
        # Store job data
        await self.redis_client.hset(
            f"job:{job_id}",
            mapping={k: json.dumps(v) if isinstance(v, (dict, list)) else str(v) 
                    for k, v in job_data.items()}
        )
        
        # Set expiry
        await self.redis_client.expire(f"job:{job_id}", settings.job_expiry_seconds)
        
        # Add to pending queue
        await self.redis_client.lpush("job_queue:pending", job_id)
        
        logger.info(f"Created job {job_id} with query: {query[:100]}...")
        return job_id
    
    async def get_job(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get job data by ID"""
        job_data = await self.redis_client.hgetall(f"job:{job_id}")
        
        if not job_data:
            return None
        
        # Parse JSON fields
        for key in ["options", "result", "error"]:
            if key in job_data and job_data[key]:
                try:
                    job_data[key] = json.loads(job_data[key])
                except json.JSONDecodeError:
                    pass
        
        return job_data
    
    async def update_job_status(self, job_id: str, status: JobStatus, 
                              result: Optional[Dict[str, Any]] = None,
                              error: Optional[str] = None):
        """Update job status and result"""
        updates = {
            "status": status,
            "updated_at": datetime.utcnow().isoformat()
        }
        
        if result:
            updates["result"] = json.dumps(result)
        
        if error:
            updates["error"] = error
        
        await self.redis_client.hset(f"job:{job_id}", mapping=updates)
        logger.info(f"Updated job {job_id} status to {status}")
    
    async def get_next_job(self) -> Optional[str]:
        """Get next job from pending queue"""
        job_id = await self.redis_client.brpop("job_queue:pending", timeout=1)
        if job_id:
            return job_id[1]  # brpop returns (key, value)
        return None
    
    async def increment_job_attempts(self, job_id: str) -> int:
        """Increment job attempt counter"""
        attempts = await self.redis_client.hincrby(f"job:{job_id}", "attempts", 1)
        await self.redis_client.hset(f"job:{job_id}", "updated_at", datetime.utcnow().isoformat())
        return attempts
    
    async def get_job_stats(self) -> Dict[str, int]:
        """Get job queue statistics"""
        pending_count = await self.redis_client.llen("job_queue:pending")

        # Count jobs by status
        status_counts = {status.value: 0 for status in JobStatus}

        # This is a simplified approach - in production, you might want to use Redis Streams
        # or maintain separate counters for better performance
        keys = await self.redis_client.keys("job:*")
        for key in keys:
            status = await self.redis_client.hget(key, "status")
            if status in status_counts:
                status_counts[status] += 1

        return {
            "pending": pending_count,
            **status_counts
        }

    async def cleanup_expired_jobs(self):
        """Clean up expired jobs (called periodically)"""
        try:
            # Get all job keys
            keys = await self.redis_client.keys("job:*")
            expired_count = 0

            for key in keys:
                ttl = await self.redis_client.ttl(key)
                if ttl == -1:  # No expiry set
                    await self.redis_client.expire(key, settings.job_expiry_seconds)
                elif ttl == -2:  # Key doesn't exist
                    expired_count += 1

            if expired_count > 0:
                logger.info(f"Cleaned up {expired_count} expired jobs")

        except Exception as e:
            logger.error(f"Error during job cleanup: {e}")


# Global Redis client instance
redis_client = RedisClient()
