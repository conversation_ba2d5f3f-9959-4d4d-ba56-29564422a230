import os
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # Redis Configuration
    redis_url: str = os.getenv("REDIS_URL", "redis://localhost:6379")
    redis_db: int = 0
    redis_password: Optional[str] = None
    
    # Selenium Configuration
    selenium_hub_url: str = os.getenv("SELENIUM_HUB_URL", "http://localhost:4444/wd/hub")
    selenium_timeout: int = 30
    page_load_timeout: int = 60
    implicit_wait: int = 10
    
    # Perplexity Configuration
    perplexity_base_url: str = "https://www.perplexity.ai"
    perplexity_search_url: str = "https://www.perplexity.ai/search"
    deep_research_timeout: int = 300  # 5 minutes for deep research
    quick_search_timeout: int = 60    # 1 minute for quick search
    
    # Job Configuration
    job_expiry_seconds: int = 3600    # 1 hour
    max_concurrent_jobs: int = 10
    job_retry_attempts: int = 3
    job_retry_delay: int = 5
    
    # API Configuration
    api_title: str = "Perplexity AI Agent"
    api_description: str = "Web automation agent for Perplexity AI queries"
    api_version: str = "1.0.0"
    debug_mode: bool = os.getenv("DEBUG_MODE", "false").lower() == "true"
    log_level: str = os.getenv("LOG_LEVEL", "INFO")
    
    # Browser Configuration
    browser_window_width: int = 1920
    browser_window_height: int = 1080
    headless_mode: bool = False  # Keep false for VNC debugging
    
    class Config:
        env_file = ".env"


# Global settings instance
settings = Settings()
