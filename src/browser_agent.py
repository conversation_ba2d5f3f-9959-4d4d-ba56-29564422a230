import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import time
import base64
from io import BytesIO

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import (
    TimeoutException, 
    NoSuchElementException, 
    WebDriverException,
    StaleElementReferenceException
)

from .config import settings
from .redis_client import redis_client, JobStatus

logger = logging.getLogger(__name__)


class BrowserAgent:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.current_tab_handles = {}
        self.is_running = False
    
    async def start(self):
        """Initialize the browser agent"""
        try:
            self.driver = self._create_driver()
            self.wait = WebDriverWait(self.driver, settings.selenium_timeout)
            self.is_running = True
            
            # Set window size
            self.driver.set_window_size(
                settings.browser_window_width, 
                settings.browser_window_height
            )
            
            logger.info("Browser agent started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start browser agent: {e}")
            raise
    
    async def stop(self):
        """Stop the browser agent and cleanup"""
        self.is_running = False
        if self.driver:
            try:
                self.driver.quit()
                logger.info("Browser agent stopped")
            except Exception as e:
                logger.error(f"Error stopping browser agent: {e}")
    
    def _create_driver(self) -> webdriver.Remote:
        """Create and configure Chrome WebDriver"""
        chrome_options = Options()
        
        # Configure Chrome options
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-plugins")
        chrome_options.add_argument("--disable-images")  # Faster loading
        chrome_options.add_argument("--disable-javascript-harmony-shipping")
        chrome_options.add_argument("--disable-background-timer-throttling")
        chrome_options.add_argument("--disable-renderer-backgrounding")
        chrome_options.add_argument("--disable-backgrounding-occluded-windows")
        chrome_options.add_argument("--disable-client-side-phishing-detection")
        chrome_options.add_argument("--disable-sync")
        chrome_options.add_argument("--disable-translate")
        chrome_options.add_argument("--hide-scrollbars")
        chrome_options.add_argument("--metrics-recording-only")
        chrome_options.add_argument("--mute-audio")
        chrome_options.add_argument("--no-first-run")
        chrome_options.add_argument("--safebrowsing-disable-auto-update")
        chrome_options.add_argument("--ignore-certificate-errors")
        chrome_options.add_argument("--ignore-ssl-errors")
        chrome_options.add_argument("--ignore-certificate-errors-spki-list")
        
        # User agent to avoid detection
        chrome_options.add_argument(
            "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
            "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
        
        if settings.headless_mode:
            chrome_options.add_argument("--headless")
        
        # Create remote WebDriver
        driver = webdriver.Remote(
            command_executor=settings.selenium_hub_url,
            options=chrome_options
        )
        
        # Set timeouts
        driver.implicitly_wait(settings.implicit_wait)
        driver.set_page_load_timeout(settings.page_load_timeout)
        
        return driver
    
    async def process_job(self, job_id: str) -> bool:
        """Process a single job"""
        try:
            # Get job details
            job_data = await redis_client.get_job(job_id)
            if not job_data:
                logger.error(f"Job {job_id} not found")
                return False
            
            # Update status to processing
            await redis_client.update_job_status(job_id, JobStatus.PROCESSING)
            
            query = job_data["query"]
            options = job_data.get("options", {})
            
            logger.info(f"Processing job {job_id}: {query[:100]}...")
            
            # Create new tab for this job
            tab_handle = self._create_new_tab()
            self.current_tab_handles[job_id] = tab_handle
            
            try:
                # Switch to the new tab
                self.driver.switch_to.window(tab_handle)
                
                # Process the query
                result = await self._execute_perplexity_query(query, options)
                
                # Update job with result
                await redis_client.update_job_status(
                    job_id, 
                    JobStatus.COMPLETED, 
                    result=result
                )
                
                logger.info(f"Job {job_id} completed successfully")
                return True
                
            except Exception as e:
                logger.error(f"Error processing job {job_id}: {e}")
                
                # Take screenshot for debugging
                screenshot = self._take_screenshot()
                
                await redis_client.update_job_status(
                    job_id, 
                    JobStatus.FAILED, 
                    error=str(e)
                )
                return False
                
            finally:
                # Cleanup tab
                self._close_tab(job_id)
        
        except Exception as e:
            logger.error(f"Unexpected error processing job {job_id}: {e}")
            await redis_client.update_job_status(job_id, JobStatus.FAILED, error=str(e))
            return False
    
    def _create_new_tab(self) -> str:
        """Create a new browser tab and return its handle"""
        self.driver.execute_script("window.open('');")
        return self.driver.window_handles[-1]
    
    def _close_tab(self, job_id: str):
        """Close the tab associated with a job"""
        if job_id in self.current_tab_handles:
            try:
                tab_handle = self.current_tab_handles[job_id]
                if tab_handle in self.driver.window_handles:
                    self.driver.switch_to.window(tab_handle)
                    self.driver.close()
                    
                    # Switch back to main tab if available
                    if self.driver.window_handles:
                        self.driver.switch_to.window(self.driver.window_handles[0])
                
                del self.current_tab_handles[job_id]
                
            except Exception as e:
                logger.error(f"Error closing tab for job {job_id}: {e}")
    
    def _take_screenshot(self) -> Optional[str]:
        """Take a screenshot and return base64 encoded string"""
        try:
            screenshot = self.driver.get_screenshot_as_png()
            return base64.b64encode(screenshot).decode('utf-8')
        except Exception as e:
            logger.error(f"Error taking screenshot: {e}")
            return None

    async def _execute_perplexity_query(self, query: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a query on Perplexity AI"""
        try:
            # Navigate to Perplexity
            self.driver.get(settings.perplexity_base_url)

            # Wait for page to load
            await asyncio.sleep(2)

            # Handle any initial popups or modals
            await self._handle_initial_popups()

            # Find and click the search input
            search_input = self.wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR,
                    "textarea[placeholder*='Ask anything'], input[placeholder*='Ask anything'], "
                    "textarea[data-testid='search-input'], input[data-testid='search-input']"))
            )

            # Clear and enter the query
            search_input.clear()
            search_input.send_keys(query)

            # Handle custom sources if specified
            sources = options.get("sources", [])
            if sources:
                await self._configure_custom_sources(sources)

            # Submit the query
            search_input.send_keys(Keys.RETURN)

            # Determine timeout based on mode
            mode = options.get("mode", "quick_search")
            timeout = (settings.deep_research_timeout if mode == "deep_research"
                      else settings.quick_search_timeout)

            # Wait for results
            result = await self._wait_for_results(timeout, mode)

            return {
                "answer": result.get("answer", ""),
                "sources": result.get("sources", []),
                "mode": mode,
                "timestamp": datetime.utcnow().isoformat(),
                "query": query
            }

        except TimeoutException:
            raise Exception(f"Timeout waiting for Perplexity results after {timeout} seconds")
        except Exception as e:
            logger.error(f"Error executing Perplexity query: {e}")
            raise

    async def _handle_initial_popups(self):
        """Handle any initial popups or modals"""
        try:
            # Common popup selectors
            popup_selectors = [
                "button[aria-label='Close']",
                "button[data-testid='close-button']",
                ".modal-close",
                "[role='dialog'] button",
                "button:contains('Skip')",
                "button:contains('Later')",
                "button:contains('No thanks')"
            ]

            for selector in popup_selectors:
                try:
                    popup = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if popup.is_displayed():
                        popup.click()
                        await asyncio.sleep(1)
                        break
                except (NoSuchElementException, StaleElementReferenceException):
                    continue

        except Exception as e:
            logger.debug(f"No popups to handle or error handling popups: {e}")

    async def _configure_custom_sources(self, sources: List[str]):
        """Configure custom sources for the search"""
        try:
            # Look for sources/focus button or similar
            focus_selectors = [
                "button[data-testid='focus-button']",
                "button:contains('Focus')",
                ".focus-button",
                "[aria-label*='source']"
            ]

            for selector in focus_selectors:
                try:
                    focus_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if focus_button.is_displayed():
                        focus_button.click()
                        await asyncio.sleep(1)

                        # Add custom sources
                        for source in sources:
                            source_input = self.driver.find_element(
                                By.CSS_SELECTOR,
                                "input[placeholder*='domain'], input[placeholder*='source']"
                            )
                            source_input.send_keys(source)
                            source_input.send_keys(Keys.RETURN)
                            await asyncio.sleep(0.5)

                        break

                except (NoSuchElementException, StaleElementReferenceException):
                    continue

        except Exception as e:
            logger.warning(f"Could not configure custom sources: {e}")

    async def _wait_for_results(self, timeout: int, mode: str) -> Dict[str, Any]:
        """Wait for and extract Perplexity results"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                # Check if results are ready
                if await self._are_results_ready():
                    return await self._extract_results()

                # For deep research, check for progress indicators
                if mode == "deep_research":
                    progress = await self._check_deep_research_progress()
                    if progress:
                        logger.info(f"Deep research progress: {progress}")

                await asyncio.sleep(2)

            except Exception as e:
                logger.error(f"Error while waiting for results: {e}")
                await asyncio.sleep(2)

        raise TimeoutException(f"Results not ready after {timeout} seconds")

    async def _are_results_ready(self) -> bool:
        """Check if results are ready"""
        try:
            # Look for result indicators
            result_selectors = [
                "[data-testid='answer']",
                ".answer-content",
                ".result-content",
                ".response-content",
                "div[role='main'] p",
                ".prose"
            ]

            for selector in result_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed() and element.text.strip():
                        return True
                except (NoSuchElementException, StaleElementReferenceException):
                    continue

            return False

        except Exception:
            return False

    async def _check_deep_research_progress(self) -> Optional[str]:
        """Check deep research progress"""
        try:
            progress_selectors = [
                ".progress-indicator",
                "[data-testid='progress']",
                ".loading-message",
                ".research-status"
            ]

            for selector in progress_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        return element.text.strip()
                except (NoSuchElementException, StaleElementReferenceException):
                    continue

            return None

        except Exception:
            return None

    async def _extract_results(self) -> Dict[str, Any]:
        """Extract the answer and sources from the page"""
        try:
            # Extract main answer
            answer = await self._extract_answer()

            # Extract sources
            sources = await self._extract_sources()

            return {
                "answer": answer,
                "sources": sources
            }

        except Exception as e:
            logger.error(f"Error extracting results: {e}")
            return {
                "answer": "Error extracting results",
                "sources": []
            }

    async def _extract_answer(self) -> str:
        """Extract the main answer text"""
        try:
            answer_selectors = [
                "[data-testid='answer']",
                ".answer-content",
                ".result-content",
                ".response-content",
                "div[role='main'] div:first-child",
                ".prose p"
            ]

            for selector in answer_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.text.strip():
                            return element.text.strip()
                except (NoSuchElementException, StaleElementReferenceException):
                    continue

            # Fallback: get all visible text
            body = self.driver.find_element(By.TAG_NAME, "body")
            return body.text[:1000] + "..." if len(body.text) > 1000 else body.text

        except Exception as e:
            logger.error(f"Error extracting answer: {e}")
            return "Could not extract answer"

    async def _extract_sources(self) -> List[Dict[str, str]]:
        """Extract source links and titles"""
        try:
            sources = []

            source_selectors = [
                ".source-link",
                ".citation",
                ".reference",
                "a[href*='http']"
            ]

            for selector in source_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            href = element.get_attribute("href")
                            title = element.text.strip() or element.get_attribute("title") or href

                            if href and href.startswith("http"):
                                sources.append({
                                    "title": title,
                                    "url": href
                                })

                                if len(sources) >= 10:  # Limit sources
                                    break

                except (NoSuchElementException, StaleElementReferenceException):
                    continue

                if sources:
                    break

            return sources

        except Exception as e:
            logger.error(f"Error extracting sources: {e}")
            return []
