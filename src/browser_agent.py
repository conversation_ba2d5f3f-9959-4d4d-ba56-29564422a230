import asyncio
import logging
import random
import json
import os
from typing import Dict, Any, Optional, List
from datetime import datetime
import time
import base64
from io import Bytes<PERSON>

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import (
    TimeoutException,
    NoSuchElementException,
    WebDriverException,
    StaleElementReferenceException
)

# Anti-detection imports
try:
    from selenium_stealth import stealth
    STEALTH_AVAILABLE = True
except ImportError:
    STEALTH_AVAILABLE = False
    logging.warning("selenium-stealth not available, stealth mode disabled")

try:
    from fake_useragent import UserAgent
    FAKE_UA_AVAILABLE = True
except ImportError:
    FAKE_UA_AVAILABLE = False
    logging.warning("fake-useragent not available, using default user agent")

try:
    import undetected_chromedriver as uc
    UC_AVAILABLE = True
except ImportError:
    UC_AVAILABLE = False
    logging.warning("undetected-chromedriver not available, using standard ChromeDriver")

try:
    from seleniumbase import Driver
    SELENIUMBASE_AVAILABLE = True
except ImportError:
    SELENIUMBASE_AVAILABLE = False
    logging.warning("seleniumbase not available")

try:
    import cloudscraper
    CLOUDSCRAPER_AVAILABLE = True
except ImportError:
    CLOUDSCRAPER_AVAILABLE = False
    logging.warning("cloudscraper not available")

from .config import settings
from .redis_client import redis_client, JobStatus

logger = logging.getLogger(__name__)


class BrowserAgent:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.current_tab_handles = {}
        self.is_running = False
        self.is_authenticated = False
        self.session_data_file = "perplexity_session.json"
        self.user_agent_generator = UserAgent() if FAKE_UA_AVAILABLE else None
    
    async def start(self):
        """Initialize the browser agent"""
        try:
            self.driver = self._create_driver()
            self.wait = WebDriverWait(self.driver, settings.page_interaction_timeout)
            self.is_running = True

            # Set window size with some randomization for anti-detection
            if settings.custom_viewport_sizes:
                width = settings.browser_window_width + random.randint(-50, 50)
                height = settings.browser_window_height + random.randint(-50, 50)
            else:
                width, height = settings.browser_window_width, settings.browser_window_height

            self.driver.set_window_size(width, height)

            # Apply stealth mode if enabled
            if settings.enable_stealth_mode and STEALTH_AVAILABLE:
                self._apply_stealth_mode()

            # Load session data if available
            if settings.session_persistence:
                await self._load_session_data()

            # Authenticate if enabled and credentials are provided
            if settings.enable_authentication and settings.perplexity_email and settings.perplexity_password:
                await self._authenticate()

            logger.info("Browser agent started successfully")

        except Exception as e:
            logger.error(f"Failed to start browser agent: {e}")
            raise
    
    async def stop(self):
        """Stop the browser agent and cleanup"""
        self.is_running = False
        if self.driver:
            try:
                self.driver.quit()
                logger.info("Browser agent stopped")
            except Exception as e:
                logger.error(f"Error stopping browser agent: {e}")
    
    def _create_driver(self) -> webdriver.Remote:
        """Create and configure Chrome WebDriver with advanced anti-detection features"""

        # Try different driver creation methods based on configuration
        if settings.use_undetected_chrome and UC_AVAILABLE:
            return self._create_undetected_driver()
        elif settings.use_seleniumbase and SELENIUMBASE_AVAILABLE:
            return self._create_seleniumbase_driver()
        else:
            return self._create_standard_driver()

    def _create_undetected_driver(self) -> webdriver.Remote:
        """Create undetected-chromedriver instance"""
        try:
            logger.info("Creating undetected ChromeDriver instance")

            # Configure undetected chrome options
            options = uc.ChromeOptions()

            # Basic options
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")

            # Cloudflare bypass specific options
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_argument("--disable-web-security")
            options.add_argument("--allow-running-insecure-content")
            options.add_argument("--disable-features=VizDisplayCompositor")

            if settings.disable_webrtc:
                options.add_argument("--disable-webrtc")

            # Set user agent
            if settings.random_user_agent and self.user_agent_generator:
                user_agent = self.user_agent_generator.random
                options.add_argument(f"--user-agent={user_agent}")
                logger.info(f"Using random user agent: {user_agent[:50]}...")

            # Language settings
            options.add_argument("--lang=en-US")
            options.add_experimental_option('prefs', {
                'intl.accept_languages': 'en-US,en;q=0.9',
                'profile.default_content_setting_values.notifications': 2,
                'profile.default_content_settings.popups': 0
            })

            if settings.headless_mode:
                options.add_argument("--headless=new")

            # Create undetected chrome driver
            # Note: This creates a local driver, we'll need to adapt for remote usage
            driver = uc.Chrome(options=options, version_main=None)

            # Set timeouts
            driver.implicitly_wait(settings.implicit_wait)
            driver.set_page_load_timeout(settings.page_load_timeout)

            logger.info("Undetected ChromeDriver created successfully")
            return driver

        except Exception as e:
            logger.error(f"Failed to create undetected ChromeDriver: {e}")
            logger.info("Falling back to standard driver")
            return self._create_standard_driver()

    def _create_seleniumbase_driver(self) -> webdriver.Remote:
        """Create SeleniumBase driver with stealth mode"""
        try:
            logger.info("Creating SeleniumBase driver instance")

            # SeleniumBase driver with stealth mode
            driver = Driver(
                uc=True,  # Use undetected mode
                headless=settings.headless_mode,
                incognito=True,
                do_not_track=True,
                no_sandbox=True,
                disable_gpu=True,
                disable_dev_shm_usage=True,
                disable_blink_features="AutomationControlled",
                disable_web_security=True,
                allow_running_insecure_content=True,
                disable_features="VizDisplayCompositor",
                user_agent=self.user_agent_generator.random if settings.random_user_agent and self.user_agent_generator else None
            )

            # Set timeouts
            driver.implicitly_wait(settings.implicit_wait)
            driver.set_page_load_timeout(settings.page_load_timeout)

            logger.info("SeleniumBase driver created successfully")
            return driver

        except Exception as e:
            logger.error(f"Failed to create SeleniumBase driver: {e}")
            logger.info("Falling back to standard driver")
            return self._create_standard_driver()

    def _create_standard_driver(self) -> webdriver.Remote:
        """Create standard Chrome WebDriver with anti-detection features"""
        chrome_options = Options()

        # Basic Chrome options for stability
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")

        # Enhanced anti-detection Chrome options for Cloudflare bypass
        if settings.enable_stealth_mode:
            # Core automation detection bypass
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # Cloudflare specific bypasses
            chrome_options.add_argument("--disable-web-security")
            chrome_options.add_argument("--allow-running-insecure-content")
            chrome_options.add_argument("--disable-features=VizDisplayCompositor")
            chrome_options.add_argument("--disable-ipc-flooding-protection")
            chrome_options.add_argument("--disable-renderer-backgrounding")
            chrome_options.add_argument("--disable-backgrounding-occluded-windows")
            chrome_options.add_argument("--disable-client-side-phishing-detection")
            chrome_options.add_argument("--disable-component-update")
            chrome_options.add_argument("--disable-default-apps")
            chrome_options.add_argument("--disable-domain-reliability")
            chrome_options.add_argument("--disable-features=TranslateUI")
            chrome_options.add_argument("--disable-hang-monitor")
            chrome_options.add_argument("--disable-prompt-on-repost")
            chrome_options.add_argument("--disable-sync")
            chrome_options.add_argument("--metrics-recording-only")
            chrome_options.add_argument("--no-first-run")
            chrome_options.add_argument("--safebrowsing-disable-auto-update")
            chrome_options.add_argument("--enable-features=NetworkService,NetworkServiceLogging")
            chrome_options.add_argument("--ignore-certificate-errors")
            chrome_options.add_argument("--ignore-ssl-errors")
            chrome_options.add_argument("--ignore-certificate-errors-spki-list")
            chrome_options.add_argument("--ignore-urlfetcher-cert-requests")

            # Additional Cloudflare bypass flags
            chrome_options.add_argument("--disable-features=VizDisplayCompositor,VizHitTestSurfaceLayer")
            chrome_options.add_argument("--disable-background-networking")
            chrome_options.add_argument("--disable-background-timer-throttling")
            chrome_options.add_argument("--disable-renderer-backgrounding")
            chrome_options.add_argument("--disable-backgrounding-occluded-windows")
            chrome_options.add_argument("--disable-client-side-phishing-detection")
            chrome_options.add_argument("--disable-crash-reporter")
            chrome_options.add_argument("--disable-oopr-debug-crash-dump")
            chrome_options.add_argument("--no-crash-upload")
            chrome_options.add_argument("--disable-low-res-tiling")
            chrome_options.add_argument("--log-level=3")

            # WebRTC bypass
            if settings.disable_webrtc:
                chrome_options.add_argument("--disable-webrtc")
                chrome_options.add_argument("--disable-webrtc-multiple-routes")
                chrome_options.add_argument("--disable-webrtc-hw-decoding")
                chrome_options.add_argument("--disable-webrtc-hw-encoding")

        # Conditional options based on configuration
        if settings.disable_extensions:
            chrome_options.add_argument("--disable-extensions")

        if settings.disable_plugins:
            chrome_options.add_argument("--disable-plugins")

        if settings.disable_images:
            chrome_options.add_argument("--disable-images")

        # Enhanced user agent handling
        if settings.random_user_agent and self.user_agent_generator:
            user_agent = self.user_agent_generator.random
            chrome_options.add_argument(f"--user-agent={user_agent}")
            logger.info(f"Using random user agent: {user_agent[:50]}...")
        else:
            # Use a very recent and common user agent
            chrome_options.add_argument(
                "--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 "
                "(KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36"
            )

        # Enhanced language and locale settings
        chrome_options.add_argument("--lang=en-US")
        chrome_options.add_experimental_option('prefs', {
            'intl.accept_languages': 'en-US,en;q=0.9',
            'profile.default_content_setting_values.notifications': 2,
            'profile.default_content_settings.popups': 0,
            'profile.managed_default_content_settings.images': 1 if not settings.disable_images else 2,
            'profile.default_content_setting_values.geolocation': 2,
            'profile.default_content_setting_values.media_stream': 2,
        })

        if settings.headless_mode:
            chrome_options.add_argument("--headless=new")
            chrome_options.add_argument("--disable-gpu")

        # Create remote WebDriver
        driver = webdriver.Remote(
            command_executor=settings.selenium_hub_url,
            options=chrome_options
        )

        # Set timeouts with extended values
        driver.implicitly_wait(settings.implicit_wait)
        driver.set_page_load_timeout(settings.page_load_timeout)

        # Execute enhanced anti-detection scripts
        if settings.enable_stealth_mode:
            self._execute_anti_detection_scripts(driver)

        return driver

    def _execute_anti_detection_scripts(self, driver):
        """Execute JavaScript to further mask automation"""
        try:
            # Remove webdriver property
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            # Override the plugins property to mimic a real browser
            driver.execute_script("""
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5]
                });
            """)

            # Override the languages property
            driver.execute_script("""
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en']
                });
            """)

            # Override the permissions property
            driver.execute_script("""
                const originalQuery = window.navigator.permissions.query;
                return window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery(parameters)
                );
            """)

            logger.debug("Anti-detection scripts executed successfully")

        except Exception as e:
            logger.warning(f"Failed to execute some anti-detection scripts: {e}")

    def _apply_stealth_mode(self):
        """Apply selenium-stealth to the driver"""
        if not STEALTH_AVAILABLE:
            logger.warning("Stealth mode requested but selenium-stealth not available")
            return

        try:
            stealth(self.driver,
                    languages=["en-US", "en"],
                    vendor="Google Inc.",
                    platform="MacIntel",  # Use Mac platform for better compatibility
                    webgl_vendor="Intel Inc.",
                    renderer="Intel Iris Pro OpenGL Engine",
                    fix_hairline=True,
                    run_on_insecure_origins=True)
            logger.info("Stealth mode applied successfully")
        except Exception as e:
            logger.error(f"Failed to apply stealth mode: {e}")

    async def _load_session_data(self):
        """Load saved session data (cookies, localStorage, etc.)"""
        try:
            if os.path.exists(self.session_data_file):
                with open(self.session_data_file, 'r') as f:
                    session_data = json.load(f)

                # Navigate to Perplexity first to set domain
                self.driver.get(settings.perplexity_base_url)
                await self._random_delay()

                # Load cookies
                if 'cookies' in session_data:
                    for cookie in session_data['cookies']:
                        try:
                            self.driver.add_cookie(cookie)
                        except Exception as e:
                            logger.debug(f"Failed to add cookie: {e}")

                # Load localStorage
                if 'localStorage' in session_data:
                    for key, value in session_data['localStorage'].items():
                        try:
                            self.driver.execute_script(
                                f"window.localStorage.setItem('{key}', '{value}');"
                            )
                        except Exception as e:
                            logger.debug(f"Failed to set localStorage item: {e}")

                logger.info("Session data loaded successfully")

        except Exception as e:
            logger.warning(f"Failed to load session data: {e}")

    async def _save_session_data(self):
        """Save current session data for persistence"""
        try:
            session_data = {
                'cookies': self.driver.get_cookies(),
                'localStorage': {}
            }

            # Get localStorage data
            try:
                local_storage = self.driver.execute_script(
                    "return Object.keys(localStorage).reduce((acc, key) => "
                    "{ acc[key] = localStorage.getItem(key); return acc; }, {});"
                )
                session_data['localStorage'] = local_storage
            except Exception as e:
                logger.debug(f"Failed to get localStorage: {e}")

            with open(self.session_data_file, 'w') as f:
                json.dump(session_data, f)

            logger.info("Session data saved successfully")

        except Exception as e:
            logger.warning(f"Failed to save session data: {e}")

    async def _authenticate(self):
        """Authenticate with Perplexity account using email verification"""
        if self.is_authenticated:
            logger.info("Already authenticated")
            return

        try:
            logger.info("Starting Perplexity email-based authentication...")

            # Navigate to login page
            self.driver.get(settings.perplexity_login_url)
            await self._random_delay(3, 6)

            # Wait for Cloudflare if needed
            if settings.cloudflare_bypass_enabled:
                await self._wait_for_cloudflare()

            # Handle any initial popups
            await self._handle_initial_popups()

            # Look for email input field with enhanced selectors
            email_selectors = [
                "input[type='email']",
                "input[name='email']",
                "input[placeholder*='email' i]",
                "input[placeholder*='Email' i]",
                "input[placeholder*='Enter your email' i]",
                "input[placeholder*='Email address' i]",
                "#email",
                "#email-input",
                ".email-input",
                "[data-testid='email']",
                "[data-testid='email-input']",
                "input[autocomplete='email']"
            ]

            email_input = None
            for selector in email_selectors:
                try:
                    email_input = self.wait.until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    logger.info(f"Found email input with selector: {selector}")
                    break
                except TimeoutException:
                    continue

            if not email_input:
                raise Exception("Could not find email input field")

            # Enter email with human-like typing
            await self._human_click(email_input)
            await self._random_delay(1, 2)
            await self._human_type(email_input, settings.perplexity_email)
            await self._random_delay(2, 3)

            # Look for submit/continue button (no password field expected)
            submit_selectors = [
                "button[type='submit']",
                "input[type='submit']",
                "button:contains('Continue')",
                "button:contains('Send')",
                "button:contains('Sign in')",
                "button:contains('Login')",
                "button:contains('Log in')",
                "button:contains('Next')",
                ".login-button",
                ".signin-button",
                ".continue-button",
                "[data-testid='submit']",
                "[data-testid='continue']",
                "[data-testid='login']"
            ]

            submit_button = None
            for selector in submit_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            submit_button = element
                            logger.info(f"Found submit button with selector: {selector}")
                            break
                    if submit_button:
                        break
                except (NoSuchElementException, StaleElementReferenceException):
                    continue

            if submit_button:
                # Click submit button with mouse simulation
                await self._human_click(submit_button)
            else:
                # Try pressing Enter on email field
                email_input.send_keys(Keys.RETURN)

            logger.info("Email submitted, waiting for verification code prompt...")
            await self._random_delay(3, 5)

            # Now handle the email verification code
            await self._handle_email_verification()

        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            raise

    async def _handle_email_verification(self):
        """Handle email verification code input"""
        try:
            logger.info("Looking for email verification code input...")

            # Look for verification code input field
            code_selectors = [
                "input[type='text'][placeholder*='code' i]",
                "input[type='text'][placeholder*='verification' i]",
                "input[type='number'][placeholder*='code' i]",
                "input[name*='code']",
                "input[name*='verification']",
                "input[name*='otp']",
                "#verification-code",
                "#code",
                "#otp",
                ".verification-input",
                ".code-input",
                "[data-testid='verification-code']",
                "[data-testid='code']",
                "[data-testid='otp']",
                "input[autocomplete='one-time-code']"
            ]

            code_input = None
            for selector in code_selectors:
                try:
                    code_input = self.wait.until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    logger.info(f"Found verification code input with selector: {selector}")
                    break
                except TimeoutException:
                    continue

            if not code_input:
                logger.warning("Could not find verification code input field")
                if settings.manual_verification:
                    await self._handle_manual_verification()
                    return
                else:
                    raise Exception("Could not find verification code input field and manual verification is disabled")

            if settings.manual_verification:
                logger.info("Manual verification enabled - waiting for user to enter code via noVNC")
                await self._wait_for_manual_code_entry(code_input)
            else:
                logger.warning("Automatic email code retrieval not implemented - enabling manual verification")
                await self._wait_for_manual_code_entry(code_input)

        except Exception as e:
            logger.error(f"Email verification failed: {e}")
            raise

    async def _handle_manual_verification(self):
        """Handle manual verification when code input field is not found"""
        logger.info("Manual verification mode - user needs to complete authentication via noVNC")
        logger.info("Please use the noVNC interface at http://localhost:7900 to complete the authentication")

        # Wait for authentication to complete (check URL changes or success indicators)
        start_time = time.time()
        while time.time() - start_time < settings.auth_timeout:
            try:
                current_url = self.driver.current_url

                # Check if we're no longer on login/verification pages
                if not any(keyword in current_url.lower() for keyword in
                          ['signin', 'login', 'verify', 'auth', 'verification']):
                    self.is_authenticated = True
                    logger.info("Manual authentication completed successfully")

                    # Save session data
                    if settings.session_persistence:
                        await self._save_session_data()
                    return

                # Check for success indicators on the page
                success_indicators = [
                    "dashboard", "profile", "settings", "welcome",
                    "[data-testid='user-menu']", ".user-avatar", ".profile-menu"
                ]

                for indicator in success_indicators:
                    try:
                        element = self.driver.find_element(By.CSS_SELECTOR, indicator)
                        if element.is_displayed():
                            self.is_authenticated = True
                            logger.info("Authentication success detected via page elements")
                            if settings.session_persistence:
                                await self._save_session_data()
                            return
                    except (NoSuchElementException, StaleElementReferenceException):
                        continue

                await asyncio.sleep(5)  # Check every 5 seconds

            except Exception as e:
                logger.debug(f"Error during manual verification check: {e}")
                await asyncio.sleep(5)

        raise Exception(f"Manual verification timeout after {settings.auth_timeout} seconds")

    async def _wait_for_manual_code_entry(self, code_input):
        """Wait for user to manually enter verification code via noVNC or API"""
        logger.info("Waiting for manual verification code entry")
        logger.info("Options:")
        logger.info("1. Enter code via noVNC interface at http://localhost:7900")
        logger.info("2. Submit code via API: POST /auth/verify-code")

        start_time = time.time()
        last_value = ""

        while time.time() - start_time < settings.auth_timeout:
            try:
                # Check for API-submitted verification code
                from .redis_client import redis_client
                api_code = await redis_client.client.get("auth_verification_code")
                if api_code:
                    logger.info("Verification code received via API")
                    # Clear the code from Redis
                    await redis_client.client.delete("auth_verification_code")

                    # Enter the code
                    code_input.clear()
                    await self._human_type(code_input, api_code.decode())
                    await self._random_delay(1, 2)

                    # Submit the code
                    await self._submit_verification_code(code_input)
                    return

                # Check for manual entry via noVNC
                current_value = code_input.get_attribute('value') or ""

                # Check if code has been entered (assuming 4-8 digit code)
                if len(current_value) >= 4 and current_value != last_value:
                    logger.info(f"Code entry detected via noVNC: {len(current_value)} characters")
                    last_value = current_value

                    # Wait a bit more for complete entry
                    await asyncio.sleep(2)

                    # Submit the code
                    await self._submit_verification_code(code_input)
                    return

                await asyncio.sleep(2)  # Check every 2 seconds

            except Exception as e:
                logger.debug(f"Error during manual code entry: {e}")
                await asyncio.sleep(2)

        raise Exception(f"Manual code entry timeout after {settings.auth_timeout} seconds")

    async def _submit_verification_code(self, code_input):
        """Submit the verification code and check for success"""
        try:
            # Check if there's a submit button to click
            submit_selectors = [
                "button[type='submit']",
                "button:contains('Verify')",
                "button:contains('Continue')",
                "button:contains('Submit')",
                "button:contains('Confirm')",
                ".verify-button",
                ".submit-button",
                ".continue-button",
                "[data-testid='verify']",
                "[data-testid='submit']"
            ]

            submit_button = None
            for selector in submit_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            submit_button = element
                            break
                    if submit_button:
                        break
                except (NoSuchElementException, StaleElementReferenceException):
                    continue

            if submit_button:
                await self._human_click(submit_button)
                logger.info("Verification code submitted via button")
            else:
                # Try pressing Enter
                code_input.send_keys(Keys.RETURN)
                logger.info("Verification code submitted via Enter key")

            # Wait for verification to complete
            await self._random_delay(3, 6)

            # Check if authentication was successful
            current_url = self.driver.current_url
            if not any(keyword in current_url.lower() for keyword in
                      ['signin', 'login', 'verify', 'auth', 'verification']):
                self.is_authenticated = True
                logger.info("Email verification completed successfully")

                # Update authentication status in Redis
                from .redis_client import redis_client
                await redis_client.client.setex("auth_status", 3600, "true")  # 1 hour expiry

                # Save session data
                if settings.session_persistence:
                    await self._save_session_data()
            else:
                logger.warning("Verification may have failed - still on auth page")

        except Exception as e:
            logger.error(f"Error submitting verification code: {e}")
            raise

    async def _wait_for_cloudflare(self):
        """Wait for Cloudflare challenge to complete"""
        if not settings.cloudflare_bypass_enabled:
            return

        try:
            logger.info("Checking for Cloudflare challenge...")

            # Check for Cloudflare indicators
            cloudflare_indicators = [
                "Checking your browser before accessing",
                "DDoS protection by Cloudflare",
                "Please wait while we are checking your browser",
                "Cloudflare Ray ID",
                "cf-browser-verification",
                "cf-challenge-running",
                "challenge-running",
                "challenge-form"
            ]

            start_time = time.time()
            while time.time() - start_time < settings.cloudflare_timeout:
                page_source = self.driver.page_source.lower()

                # Check if Cloudflare challenge is present
                cloudflare_detected = any(indicator.lower() in page_source for indicator in cloudflare_indicators)

                if cloudflare_detected:
                    logger.info("Cloudflare challenge detected, waiting for completion...")
                    await asyncio.sleep(5)
                    continue

                # Check for challenge completion indicators
                if "challenge-success" in page_source or "cf-challenge-success" in page_source:
                    logger.info("Cloudflare challenge completed successfully")
                    break

                # Check if we can find normal page content
                try:
                    # Look for typical Perplexity page elements
                    normal_elements = [
                        "textarea[placeholder*='Ask anything']",
                        "input[placeholder*='Ask anything']",
                        ".search-input",
                        "[data-testid='search-input']"
                    ]

                    for selector in normal_elements:
                        try:
                            element = self.driver.find_element(By.CSS_SELECTOR, selector)
                            if element.is_displayed():
                                logger.info("Normal page content detected, Cloudflare bypass successful")
                                return
                        except (NoSuchElementException, StaleElementReferenceException):
                            continue

                except Exception:
                    pass

                await asyncio.sleep(2)

            # Final check
            page_source = self.driver.page_source.lower()
            if any(indicator.lower() in page_source for indicator in cloudflare_indicators):
                logger.warning("Cloudflare challenge may still be active")
            else:
                logger.info("Cloudflare check completed")

        except Exception as e:
            logger.warning(f"Error during Cloudflare check: {e}")

    async def _detect_cloudflare_challenge(self) -> bool:
        """Detect if Cloudflare challenge is present"""
        try:
            page_source = self.driver.page_source.lower()
            title = self.driver.title.lower()

            cloudflare_indicators = [
                "checking your browser before accessing",
                "ddos protection by cloudflare",
                "please wait while we are checking your browser",
                "cloudflare ray id",
                "attention required",
                "cf-browser-verification",
                "cf-challenge-running",
                "challenge-running",
                "challenge-form",
                "just a moment",
                "please enable javascript and cookies to continue"
            ]

            # Check page source and title
            for indicator in cloudflare_indicators:
                if indicator in page_source or indicator in title:
                    return True

            # Check for Cloudflare-specific elements
            cloudflare_selectors = [
                ".cf-browser-verification",
                ".cf-challenge-running",
                "#challenge-running",
                "#challenge-form",
                ".challenge-form"
            ]

            for selector in cloudflare_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        return True
                except (NoSuchElementException, StaleElementReferenceException):
                    continue

            return False

        except Exception as e:
            logger.debug(f"Error detecting Cloudflare challenge: {e}")
            return False

    async def _random_delay(self, min_delay: float = None, max_delay: float = None):
        """Add random delay to simulate human behavior"""
        if not settings.simulate_human_behavior:
            return

        min_delay = min_delay or settings.random_delays_min
        max_delay = max_delay or settings.random_delays_max
        delay = random.uniform(min_delay, max_delay)
        await asyncio.sleep(delay)

    async def _human_type(self, element, text: str):
        """Type text with human-like delays and patterns"""
        if not settings.simulate_human_behavior:
            element.clear()
            element.send_keys(text)
            return

        element.clear()
        await self._random_delay(0.5, 1.0)

        for char in text:
            element.send_keys(char)
            # Random delay between keystrokes
            await asyncio.sleep(random.uniform(0.05, 0.15))

            # Occasional longer pauses (simulating thinking)
            if random.random() < 0.1:
                await asyncio.sleep(random.uniform(0.3, 0.8))

    async def _human_click(self, element):
        """Click element with human-like mouse movement"""
        if not settings.simulate_human_behavior or not settings.mouse_movement_simulation:
            element.click()
            return

        try:
            # Move to element with ActionChains for more realistic interaction
            actions = ActionChains(self.driver)
            actions.move_to_element(element)
            actions.pause(random.uniform(0.1, 0.3))
            actions.click()
            actions.perform()

            await self._random_delay(0.2, 0.5)

        except Exception as e:
            logger.debug(f"Human click failed, falling back to regular click: {e}")
            element.click()

    async def _simulate_reading_delay(self, content_length: int = 100):
        """Simulate time spent reading content"""
        if not settings.simulate_human_behavior:
            return

        # Estimate reading time (average 200 words per minute)
        words_estimate = content_length / 5  # Rough estimate: 5 chars per word
        reading_time = (words_estimate / 200) * 60  # Convert to seconds

        # Add some randomness and cap the delay
        reading_delay = min(random.uniform(reading_time * 0.5, reading_time * 1.5), 10)
        await asyncio.sleep(reading_delay)
    
    async def process_job(self, job_id: str) -> bool:
        """Process a single job"""
        try:
            # Get job details
            job_data = await redis_client.get_job(job_id)
            if not job_data:
                logger.error(f"Job {job_id} not found")
                return False
            
            # Update status to processing
            await redis_client.update_job_status(job_id, JobStatus.PROCESSING)
            
            query = job_data["query"]
            options = job_data.get("options", {})
            
            logger.info(f"Processing job {job_id}: {query[:100]}...")
            
            # Create new tab for this job
            tab_handle = self._create_new_tab()
            self.current_tab_handles[job_id] = tab_handle
            
            try:
                # Switch to the new tab
                self.driver.switch_to.window(tab_handle)
                
                # Process the query
                result = await self._execute_perplexity_query(query, options)
                
                # Update job with result
                await redis_client.update_job_status(
                    job_id, 
                    JobStatus.COMPLETED, 
                    result=result
                )
                
                logger.info(f"Job {job_id} completed successfully")
                return True
                
            except Exception as e:
                logger.error(f"Error processing job {job_id}: {e}")
                
                # Take screenshot for debugging
                screenshot = self._take_screenshot()
                
                await redis_client.update_job_status(
                    job_id, 
                    JobStatus.FAILED, 
                    error=str(e)
                )
                return False
                
            finally:
                # Cleanup tab
                self._close_tab(job_id)
        
        except Exception as e:
            logger.error(f"Unexpected error processing job {job_id}: {e}")
            await redis_client.update_job_status(job_id, JobStatus.FAILED, error=str(e))
            return False
    
    def _create_new_tab(self) -> str:
        """Create a new browser tab and return its handle"""
        self.driver.execute_script("window.open('');")
        return self.driver.window_handles[-1]
    
    def _close_tab(self, job_id: str):
        """Close the tab associated with a job"""
        if job_id in self.current_tab_handles:
            try:
                tab_handle = self.current_tab_handles[job_id]
                if tab_handle in self.driver.window_handles:
                    self.driver.switch_to.window(tab_handle)
                    self.driver.close()
                    
                    # Switch back to main tab if available
                    if self.driver.window_handles:
                        self.driver.switch_to.window(self.driver.window_handles[0])
                
                del self.current_tab_handles[job_id]
                
            except Exception as e:
                logger.error(f"Error closing tab for job {job_id}: {e}")
    
    def _take_screenshot(self) -> Optional[str]:
        """Take a screenshot and return base64 encoded string"""
        try:
            screenshot = self.driver.get_screenshot_as_png()
            return base64.b64encode(screenshot).decode('utf-8')
        except Exception as e:
            logger.error(f"Error taking screenshot: {e}")
            return None

    async def _execute_perplexity_query(self, query: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a query on Perplexity AI with enhanced interface detection and Cloudflare bypass"""
        try:
            # Navigate to Perplexity
            logger.info(f"Navigating to Perplexity: {settings.perplexity_base_url}")
            self.driver.get(settings.perplexity_base_url)
            await self._random_delay(3, 6)

            # Wait for Cloudflare challenge if present
            if settings.cloudflare_bypass_enabled:
                await self._wait_for_cloudflare()

            # Handle any initial popups or modals
            await self._handle_initial_popups()

            # Enhanced search input selectors based on current Perplexity interface
            search_input_selectors = [
                # Main search textarea (most common)
                "textarea[placeholder*='Ask anything']",
                "textarea[placeholder*='ask anything']",
                "textarea[data-testid='search-input']",
                "textarea[data-testid='searchbox']",
                "textarea[aria-label*='search']",
                "textarea[aria-label*='Ask']",

                # Alternative input types
                "input[placeholder*='Ask anything']",
                "input[placeholder*='ask anything']",
                "input[data-testid='search-input']",
                "input[data-testid='searchbox']",
                "input[type='text'][placeholder*='search']",

                # Generic selectors
                "textarea:not([style*='display: none'])",
                "input[type='text']:not([style*='display: none'])",

                # Class-based selectors (may change)
                ".search-input",
                ".query-input",
                ".main-search",
                "[role='textbox']"
            ]

            search_input = None
            for selector in search_input_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            search_input = element
                            logger.info(f"Found search input with selector: {selector}")
                            break
                    if search_input:
                        break
                except (NoSuchElementException, StaleElementReferenceException):
                    continue

            if not search_input:
                # Try waiting for any visible input/textarea
                try:
                    search_input = self.wait.until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, "textarea, input[type='text']"))
                    )
                except TimeoutException:
                    raise Exception("Could not find search input field on Perplexity")

            # Click on the search input to focus it
            await self._human_click(search_input)
            await self._random_delay()

            # Clear and enter the query with human-like typing
            await self._human_type(search_input, query)
            await self._random_delay()

            # Handle custom sources if specified
            sources = options.get("sources", [])
            if sources:
                await self._configure_custom_sources(sources)

            # Submit the query (try Enter key first, then look for submit button)
            try:
                search_input.send_keys(Keys.RETURN)
                logger.info("Query submitted with Enter key")
            except Exception as e:
                logger.warning(f"Enter key failed, looking for submit button: {e}")
                await self._find_and_click_submit_button()

            await self._random_delay(1, 2)

            # Determine timeout based on mode
            mode = options.get("mode", "quick_search")
            timeout = (settings.deep_research_timeout if mode == "deep_research"
                      else settings.quick_search_timeout)

            logger.info(f"Waiting for results (mode: {mode}, timeout: {timeout}s)")

            # Wait for results
            result = await self._wait_for_results(timeout, mode)

            return {
                "answer": result.get("answer", ""),
                "sources": result.get("sources", []),
                "mode": mode,
                "timestamp": datetime.utcnow().isoformat(),
                "query": query,
                "processing_time": result.get("processing_time", 0)
            }

        except TimeoutException:
            raise Exception(f"Timeout waiting for Perplexity results after {timeout} seconds")
        except Exception as e:
            logger.error(f"Error executing Perplexity query: {e}")
            raise

    async def _find_and_click_submit_button(self):
        """Find and click the submit button if Enter key doesn't work"""
        submit_selectors = [
            "button[type='submit']",
            "button[aria-label*='search']",
            "button[aria-label*='submit']",
            "button[data-testid='submit']",
            "button[data-testid='search-button']",
            ".search-button",
            ".submit-button",
            "button:contains('Search')",
            "button svg[*|href*='search']",  # SVG search icons
            "button[title*='search']"
        ]

        for selector in submit_selectors:
            try:
                button = self.driver.find_element(By.CSS_SELECTOR, selector)
                if button.is_displayed() and button.is_enabled():
                    await self._human_click(button)
                    logger.info(f"Clicked submit button with selector: {selector}")
                    return
            except (NoSuchElementException, StaleElementReferenceException):
                continue

        logger.warning("Could not find submit button")

    async def _handle_initial_popups(self):
        """Handle any initial popups or modals"""
        try:
            # Common popup selectors
            popup_selectors = [
                "button[aria-label='Close']",
                "button[data-testid='close-button']",
                ".modal-close",
                "[role='dialog'] button",
                "button:contains('Skip')",
                "button:contains('Later')",
                "button:contains('No thanks')"
            ]

            for selector in popup_selectors:
                try:
                    popup = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if popup.is_displayed():
                        popup.click()
                        await asyncio.sleep(1)
                        break
                except (NoSuchElementException, StaleElementReferenceException):
                    continue

        except Exception as e:
            logger.debug(f"No popups to handle or error handling popups: {e}")

    async def _configure_custom_sources(self, sources: List[str]):
        """Configure custom sources for the search"""
        try:
            # Look for sources/focus button or similar
            focus_selectors = [
                "button[data-testid='focus-button']",
                "button:contains('Focus')",
                ".focus-button",
                "[aria-label*='source']"
            ]

            for selector in focus_selectors:
                try:
                    focus_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if focus_button.is_displayed():
                        focus_button.click()
                        await asyncio.sleep(1)

                        # Add custom sources
                        for source in sources:
                            source_input = self.driver.find_element(
                                By.CSS_SELECTOR,
                                "input[placeholder*='domain'], input[placeholder*='source']"
                            )
                            source_input.send_keys(source)
                            source_input.send_keys(Keys.RETURN)
                            await asyncio.sleep(0.5)

                        break

                except (NoSuchElementException, StaleElementReferenceException):
                    continue

        except Exception as e:
            logger.warning(f"Could not configure custom sources: {e}")

    async def _wait_for_results(self, timeout: int, mode: str) -> Dict[str, Any]:
        """Wait for and extract Perplexity results with enhanced detection"""
        start_time = time.time()
        last_progress_log = 0

        logger.info(f"Starting to wait for results (timeout: {timeout}s)")

        while time.time() - start_time < timeout:
            try:
                elapsed = time.time() - start_time

                # Log progress every 30 seconds
                if elapsed - last_progress_log > 30:
                    logger.info(f"Still waiting for results... ({elapsed:.0f}s elapsed)")
                    last_progress_log = elapsed

                # Check if results are ready
                if await self._are_results_ready():
                    processing_time = time.time() - start_time
                    logger.info(f"Results ready after {processing_time:.1f} seconds")
                    result = await self._extract_results()
                    result["processing_time"] = processing_time
                    return result

                # For deep research, check for progress indicators
                if mode == "deep_research":
                    progress = await self._check_deep_research_progress()
                    if progress:
                        logger.info(f"Deep research progress: {progress}")

                # Check for error states
                if await self._check_for_errors():
                    raise Exception("Perplexity returned an error state")

                # Adaptive sleep - shorter intervals initially, longer as time goes on
                if elapsed < 30:
                    await asyncio.sleep(2)
                elif elapsed < 120:
                    await asyncio.sleep(5)
                else:
                    await asyncio.sleep(10)

            except Exception as e:
                logger.error(f"Error while waiting for results: {e}")
                await asyncio.sleep(5)

        raise TimeoutException(f"Results not ready after {timeout} seconds")

    async def _are_results_ready(self) -> bool:
        """Check if results are ready with enhanced selectors"""
        try:
            # Enhanced result detection selectors
            result_selectors = [
                # Main answer containers
                "[data-testid='answer']",
                "[data-testid='copilot-answer']",
                "[data-testid='answer-content']",
                ".answer-content",
                ".result-content",
                ".response-content",
                ".copilot-answer",

                # Content areas
                "div[role='main'] div[class*='answer']",
                "div[role='main'] div[class*='response']",
                "div[role='main'] div[class*='result']",
                "main div[class*='answer']",
                "main div[class*='response']",

                # Text content areas
                ".prose",
                ".markdown",
                ".content",
                "div[class*='prose']",
                "div[class*='markdown']",

                # Generic content selectors
                "div[role='main'] p:not(:empty)",
                "main p:not(:empty)",
                "article p:not(:empty)",

                # Fallback selectors
                "div:contains('Based on')",
                "div:contains('According to')",
                "div:contains('The answer')"
            ]

            for selector in result_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            text = element.text.strip()
                            # Check for meaningful content (not just loading indicators)
                            if (len(text) > 20 and
                                not any(indicator in text.lower() for indicator in
                                       ['loading', 'searching', 'thinking', 'generating', '...'])):
                                logger.debug(f"Found result content with selector: {selector}")
                                return True
                except (NoSuchElementException, StaleElementReferenceException):
                    continue

            # Check for streaming content indicators
            streaming_selectors = [
                ".streaming",
                ".typing",
                "[data-testid='streaming']",
                ".cursor-blink"
            ]

            for selector in streaming_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        logger.debug("Found streaming indicator, content still generating")
                        return False
                except (NoSuchElementException, StaleElementReferenceException):
                    continue

            return False

        except Exception as e:
            logger.debug(f"Error checking if results are ready: {e}")
            return False

    async def _check_for_errors(self) -> bool:
        """Check for error states on the page"""
        try:
            error_selectors = [
                ".error",
                ".error-message",
                "[data-testid='error']",
                ".alert-error",
                ".notification-error",
                "div:contains('Error')",
                "div:contains('Something went wrong')",
                "div:contains('Try again')"
            ]

            for selector in error_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        logger.warning(f"Found error indicator: {element.text}")
                        return True
                except (NoSuchElementException, StaleElementReferenceException):
                    continue

            return False

        except Exception:
            return False

    async def _check_deep_research_progress(self) -> Optional[str]:
        """Check deep research progress"""
        try:
            progress_selectors = [
                ".progress-indicator",
                "[data-testid='progress']",
                ".loading-message",
                ".research-status"
            ]

            for selector in progress_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        return element.text.strip()
                except (NoSuchElementException, StaleElementReferenceException):
                    continue

            return None

        except Exception:
            return None

    async def _extract_results(self) -> Dict[str, Any]:
        """Extract the answer and sources from the page"""
        try:
            # Extract main answer
            answer = await self._extract_answer()

            # Extract sources
            sources = await self._extract_sources()

            return {
                "answer": answer,
                "sources": sources
            }

        except Exception as e:
            logger.error(f"Error extracting results: {e}")
            return {
                "answer": "Error extracting results",
                "sources": []
            }

    async def _extract_answer(self) -> str:
        """Extract the main answer text"""
        try:
            answer_selectors = [
                "[data-testid='answer']",
                ".answer-content",
                ".result-content",
                ".response-content",
                "div[role='main'] div:first-child",
                ".prose p"
            ]

            for selector in answer_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.text.strip():
                            return element.text.strip()
                except (NoSuchElementException, StaleElementReferenceException):
                    continue

            # Fallback: get all visible text
            body = self.driver.find_element(By.TAG_NAME, "body")
            return body.text[:1000] + "..." if len(body.text) > 1000 else body.text

        except Exception as e:
            logger.error(f"Error extracting answer: {e}")
            return "Could not extract answer"

    async def _extract_sources(self) -> List[Dict[str, str]]:
        """Extract source links and titles"""
        try:
            sources = []

            source_selectors = [
                ".source-link",
                ".citation",
                ".reference",
                "a[href*='http']"
            ]

            for selector in source_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            href = element.get_attribute("href")
                            title = element.text.strip() or element.get_attribute("title") or href

                            if href and href.startswith("http"):
                                sources.append({
                                    "title": title,
                                    "url": href
                                })

                                if len(sources) >= 10:  # Limit sources
                                    break

                except (NoSuchElementException, StaleElementReferenceException):
                    continue

                if sources:
                    break

            return sources

        except Exception as e:
            logger.error(f"Error extracting sources: {e}")
            return []
