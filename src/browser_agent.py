import asyncio
import logging
import random
import json
import os
from typing import Dict, Any, Optional, List
from datetime import datetime
import time
import base64
from io import BytesIO

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import (
    TimeoutException,
    NoSuchElementException,
    WebDriverException,
    StaleElementReferenceException
)

# Anti-detection imports
try:
    from selenium_stealth import stealth
    STEALTH_AVAILABLE = True
except ImportError:
    STEALTH_AVAILABLE = False
    logging.warning("selenium-stealth not available, stealth mode disabled")

try:
    from fake_useragent import UserAgent
    FAKE_UA_AVAILABLE = True
except ImportError:
    FAKE_UA_AVAILABLE = False
    logging.warning("fake-useragent not available, using default user agent")

try:
    import undetected_chromedriver as uc
    UC_AVAILABLE = True
except ImportError:
    UC_AVAILABLE = False
    logging.warning("undetected-chromedriver not available, using standard ChromeDriver")

from .config import settings
from .redis_client import redis_client, JobStatus

logger = logging.getLogger(__name__)


class BrowserAgent:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.current_tab_handles = {}
        self.is_running = False
        self.is_authenticated = False
        self.session_data_file = "perplexity_session.json"
        self.user_agent_generator = UserAgent() if FAKE_UA_AVAILABLE else None
    
    async def start(self):
        """Initialize the browser agent"""
        try:
            self.driver = self._create_driver()
            self.wait = WebDriverWait(self.driver, settings.page_interaction_timeout)
            self.is_running = True

            # Set window size with some randomization for anti-detection
            if settings.custom_viewport_sizes:
                width = settings.browser_window_width + random.randint(-50, 50)
                height = settings.browser_window_height + random.randint(-50, 50)
            else:
                width, height = settings.browser_window_width, settings.browser_window_height

            self.driver.set_window_size(width, height)

            # Apply stealth mode if enabled
            if settings.enable_stealth_mode and STEALTH_AVAILABLE:
                self._apply_stealth_mode()

            # Load session data if available
            if settings.session_persistence:
                await self._load_session_data()

            # Authenticate if enabled and credentials are provided
            if settings.enable_authentication and settings.perplexity_email and settings.perplexity_password:
                await self._authenticate()

            logger.info("Browser agent started successfully")

        except Exception as e:
            logger.error(f"Failed to start browser agent: {e}")
            raise
    
    async def stop(self):
        """Stop the browser agent and cleanup"""
        self.is_running = False
        if self.driver:
            try:
                self.driver.quit()
                logger.info("Browser agent stopped")
            except Exception as e:
                logger.error(f"Error stopping browser agent: {e}")
    
    def _create_driver(self) -> webdriver.Remote:
        """Create and configure Chrome WebDriver with anti-detection features"""
        chrome_options = Options()

        # Basic Chrome options for stability
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")

        # Anti-detection Chrome options
        if settings.enable_stealth_mode:
            # Disable automation indicators
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # Disable WebRTC if configured
            if settings.disable_webrtc:
                chrome_options.add_argument("--disable-webrtc")
                chrome_options.add_argument("--disable-webrtc-multiple-routes")
                chrome_options.add_argument("--disable-webrtc-hw-decoding")
                chrome_options.add_argument("--disable-webrtc-hw-encoding")

            # Additional anti-detection flags
            chrome_options.add_argument("--disable-features=VizDisplayCompositor")
            chrome_options.add_argument("--disable-ipc-flooding-protection")
            chrome_options.add_argument("--disable-renderer-backgrounding")
            chrome_options.add_argument("--disable-backgrounding-occluded-windows")
            chrome_options.add_argument("--disable-client-side-phishing-detection")
            chrome_options.add_argument("--disable-component-update")
            chrome_options.add_argument("--disable-default-apps")
            chrome_options.add_argument("--disable-domain-reliability")
            chrome_options.add_argument("--disable-features=TranslateUI")
            chrome_options.add_argument("--disable-hang-monitor")
            chrome_options.add_argument("--disable-prompt-on-repost")
            chrome_options.add_argument("--disable-sync")
            chrome_options.add_argument("--disable-web-security")
            chrome_options.add_argument("--metrics-recording-only")
            chrome_options.add_argument("--no-first-run")
            chrome_options.add_argument("--safebrowsing-disable-auto-update")
            chrome_options.add_argument("--enable-features=NetworkService,NetworkServiceLogging")
            chrome_options.add_argument("--ignore-certificate-errors")
            chrome_options.add_argument("--ignore-ssl-errors")
            chrome_options.add_argument("--ignore-certificate-errors-spki-list")
            chrome_options.add_argument("--ignore-urlfetcher-cert-requests")

        # Conditional options based on configuration
        if settings.disable_extensions:
            chrome_options.add_argument("--disable-extensions")

        if settings.disable_plugins:
            chrome_options.add_argument("--disable-plugins")

        if settings.disable_images:
            chrome_options.add_argument("--disable-images")

        # Random or configured user agent
        if settings.random_user_agent and self.user_agent_generator:
            user_agent = self.user_agent_generator.random
            chrome_options.add_argument(f"--user-agent={user_agent}")
            logger.info(f"Using random user agent: {user_agent[:50]}...")
        else:
            # Default realistic user agent
            chrome_options.add_argument(
                "--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 "
                "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )

        # Language and locale settings
        chrome_options.add_argument("--lang=en-US")
        chrome_options.add_experimental_option('prefs', {
            'intl.accept_languages': 'en-US,en;q=0.9',
            'profile.default_content_setting_values.notifications': 2,
            'profile.default_content_settings.popups': 0,
            'profile.managed_default_content_settings.images': 1 if not settings.disable_images else 2
        })

        if settings.headless_mode:
            chrome_options.add_argument("--headless=new")  # Use new headless mode
            chrome_options.add_argument("--disable-gpu")

        # Create remote WebDriver
        driver = webdriver.Remote(
            command_executor=settings.selenium_hub_url,
            options=chrome_options
        )

        # Set timeouts with extended values
        driver.implicitly_wait(settings.implicit_wait)
        driver.set_page_load_timeout(settings.page_load_timeout)

        # Execute script to remove webdriver property
        if settings.enable_stealth_mode:
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        return driver

    def _apply_stealth_mode(self):
        """Apply selenium-stealth to the driver"""
        if not STEALTH_AVAILABLE:
            logger.warning("Stealth mode requested but selenium-stealth not available")
            return

        try:
            stealth(self.driver,
                    languages=["en-US", "en"],
                    vendor="Google Inc.",
                    platform="MacIntel",  # Use Mac platform for better compatibility
                    webgl_vendor="Intel Inc.",
                    renderer="Intel Iris Pro OpenGL Engine",
                    fix_hairline=True,
                    run_on_insecure_origins=True)
            logger.info("Stealth mode applied successfully")
        except Exception as e:
            logger.error(f"Failed to apply stealth mode: {e}")

    async def _load_session_data(self):
        """Load saved session data (cookies, localStorage, etc.)"""
        try:
            if os.path.exists(self.session_data_file):
                with open(self.session_data_file, 'r') as f:
                    session_data = json.load(f)

                # Navigate to Perplexity first to set domain
                self.driver.get(settings.perplexity_base_url)
                await self._random_delay()

                # Load cookies
                if 'cookies' in session_data:
                    for cookie in session_data['cookies']:
                        try:
                            self.driver.add_cookie(cookie)
                        except Exception as e:
                            logger.debug(f"Failed to add cookie: {e}")

                # Load localStorage
                if 'localStorage' in session_data:
                    for key, value in session_data['localStorage'].items():
                        try:
                            self.driver.execute_script(
                                f"window.localStorage.setItem('{key}', '{value}');"
                            )
                        except Exception as e:
                            logger.debug(f"Failed to set localStorage item: {e}")

                logger.info("Session data loaded successfully")

        except Exception as e:
            logger.warning(f"Failed to load session data: {e}")

    async def _save_session_data(self):
        """Save current session data for persistence"""
        try:
            session_data = {
                'cookies': self.driver.get_cookies(),
                'localStorage': {}
            }

            # Get localStorage data
            try:
                local_storage = self.driver.execute_script(
                    "return Object.keys(localStorage).reduce((acc, key) => "
                    "{ acc[key] = localStorage.getItem(key); return acc; }, {});"
                )
                session_data['localStorage'] = local_storage
            except Exception as e:
                logger.debug(f"Failed to get localStorage: {e}")

            with open(self.session_data_file, 'w') as f:
                json.dump(session_data, f)

            logger.info("Session data saved successfully")

        except Exception as e:
            logger.warning(f"Failed to save session data: {e}")

    async def _authenticate(self):
        """Authenticate with Perplexity account"""
        if self.is_authenticated:
            logger.info("Already authenticated")
            return

        try:
            logger.info("Starting Perplexity authentication...")

            # Navigate to login page
            self.driver.get(settings.perplexity_login_url)
            await self._random_delay(2, 4)

            # Handle any initial popups
            await self._handle_initial_popups()

            # Look for email input field
            email_selectors = [
                "input[type='email']",
                "input[name='email']",
                "input[placeholder*='email' i]",
                "input[placeholder*='Email' i]",
                "#email",
                ".email-input"
            ]

            email_input = None
            for selector in email_selectors:
                try:
                    email_input = self.wait.until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    break
                except TimeoutException:
                    continue

            if not email_input:
                raise Exception("Could not find email input field")

            # Enter email with human-like typing
            await self._human_type(email_input, settings.perplexity_email)
            await self._random_delay()

            # Look for password input field
            password_selectors = [
                "input[type='password']",
                "input[name='password']",
                "input[placeholder*='password' i]",
                "input[placeholder*='Password' i]",
                "#password",
                ".password-input"
            ]

            password_input = None
            for selector in password_selectors:
                try:
                    password_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if password_input.is_displayed():
                        break
                except (NoSuchElementException, StaleElementReferenceException):
                    continue

            if not password_input:
                raise Exception("Could not find password input field")

            # Enter password with human-like typing
            await self._human_type(password_input, settings.perplexity_password)
            await self._random_delay()

            # Look for submit button
            submit_selectors = [
                "button[type='submit']",
                "input[type='submit']",
                "button:contains('Sign in')",
                "button:contains('Login')",
                "button:contains('Log in')",
                ".login-button",
                ".signin-button"
            ]

            submit_button = None
            for selector in submit_selectors:
                try:
                    submit_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if submit_button.is_displayed() and submit_button.is_enabled():
                        break
                except (NoSuchElementException, StaleElementReferenceException):
                    continue

            if not submit_button:
                # Try pressing Enter on password field
                password_input.send_keys(Keys.RETURN)
            else:
                # Click submit button with mouse simulation
                await self._human_click(submit_button)

            # Wait for authentication to complete
            await self._random_delay(3, 6)

            # Check if authentication was successful
            current_url = self.driver.current_url
            if "signin" not in current_url.lower() and "login" not in current_url.lower():
                self.is_authenticated = True
                logger.info("Authentication successful")

                # Save session data for future use
                if settings.session_persistence:
                    await self._save_session_data()
            else:
                raise Exception("Authentication failed - still on login page")

        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            raise

    async def _random_delay(self, min_delay: float = None, max_delay: float = None):
        """Add random delay to simulate human behavior"""
        if not settings.simulate_human_behavior:
            return

        min_delay = min_delay or settings.random_delays_min
        max_delay = max_delay or settings.random_delays_max
        delay = random.uniform(min_delay, max_delay)
        await asyncio.sleep(delay)

    async def _human_type(self, element, text: str):
        """Type text with human-like delays and patterns"""
        if not settings.simulate_human_behavior:
            element.clear()
            element.send_keys(text)
            return

        element.clear()
        await self._random_delay(0.5, 1.0)

        for char in text:
            element.send_keys(char)
            # Random delay between keystrokes
            await asyncio.sleep(random.uniform(0.05, 0.15))

            # Occasional longer pauses (simulating thinking)
            if random.random() < 0.1:
                await asyncio.sleep(random.uniform(0.3, 0.8))

    async def _human_click(self, element):
        """Click element with human-like mouse movement"""
        if not settings.simulate_human_behavior or not settings.mouse_movement_simulation:
            element.click()
            return

        try:
            # Move to element with ActionChains for more realistic interaction
            actions = ActionChains(self.driver)
            actions.move_to_element(element)
            actions.pause(random.uniform(0.1, 0.3))
            actions.click()
            actions.perform()

            await self._random_delay(0.2, 0.5)

        except Exception as e:
            logger.debug(f"Human click failed, falling back to regular click: {e}")
            element.click()

    async def _simulate_reading_delay(self, content_length: int = 100):
        """Simulate time spent reading content"""
        if not settings.simulate_human_behavior:
            return

        # Estimate reading time (average 200 words per minute)
        words_estimate = content_length / 5  # Rough estimate: 5 chars per word
        reading_time = (words_estimate / 200) * 60  # Convert to seconds

        # Add some randomness and cap the delay
        reading_delay = min(random.uniform(reading_time * 0.5, reading_time * 1.5), 10)
        await asyncio.sleep(reading_delay)
    
    async def process_job(self, job_id: str) -> bool:
        """Process a single job"""
        try:
            # Get job details
            job_data = await redis_client.get_job(job_id)
            if not job_data:
                logger.error(f"Job {job_id} not found")
                return False
            
            # Update status to processing
            await redis_client.update_job_status(job_id, JobStatus.PROCESSING)
            
            query = job_data["query"]
            options = job_data.get("options", {})
            
            logger.info(f"Processing job {job_id}: {query[:100]}...")
            
            # Create new tab for this job
            tab_handle = self._create_new_tab()
            self.current_tab_handles[job_id] = tab_handle
            
            try:
                # Switch to the new tab
                self.driver.switch_to.window(tab_handle)
                
                # Process the query
                result = await self._execute_perplexity_query(query, options)
                
                # Update job with result
                await redis_client.update_job_status(
                    job_id, 
                    JobStatus.COMPLETED, 
                    result=result
                )
                
                logger.info(f"Job {job_id} completed successfully")
                return True
                
            except Exception as e:
                logger.error(f"Error processing job {job_id}: {e}")
                
                # Take screenshot for debugging
                screenshot = self._take_screenshot()
                
                await redis_client.update_job_status(
                    job_id, 
                    JobStatus.FAILED, 
                    error=str(e)
                )
                return False
                
            finally:
                # Cleanup tab
                self._close_tab(job_id)
        
        except Exception as e:
            logger.error(f"Unexpected error processing job {job_id}: {e}")
            await redis_client.update_job_status(job_id, JobStatus.FAILED, error=str(e))
            return False
    
    def _create_new_tab(self) -> str:
        """Create a new browser tab and return its handle"""
        self.driver.execute_script("window.open('');")
        return self.driver.window_handles[-1]
    
    def _close_tab(self, job_id: str):
        """Close the tab associated with a job"""
        if job_id in self.current_tab_handles:
            try:
                tab_handle = self.current_tab_handles[job_id]
                if tab_handle in self.driver.window_handles:
                    self.driver.switch_to.window(tab_handle)
                    self.driver.close()
                    
                    # Switch back to main tab if available
                    if self.driver.window_handles:
                        self.driver.switch_to.window(self.driver.window_handles[0])
                
                del self.current_tab_handles[job_id]
                
            except Exception as e:
                logger.error(f"Error closing tab for job {job_id}: {e}")
    
    def _take_screenshot(self) -> Optional[str]:
        """Take a screenshot and return base64 encoded string"""
        try:
            screenshot = self.driver.get_screenshot_as_png()
            return base64.b64encode(screenshot).decode('utf-8')
        except Exception as e:
            logger.error(f"Error taking screenshot: {e}")
            return None

    async def _execute_perplexity_query(self, query: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a query on Perplexity AI with enhanced interface detection"""
        try:
            # Navigate to Perplexity
            self.driver.get(settings.perplexity_base_url)
            await self._random_delay(2, 4)

            # Handle any initial popups or modals
            await self._handle_initial_popups()

            # Enhanced search input selectors based on current Perplexity interface
            search_input_selectors = [
                # Main search textarea (most common)
                "textarea[placeholder*='Ask anything']",
                "textarea[placeholder*='ask anything']",
                "textarea[data-testid='search-input']",
                "textarea[data-testid='searchbox']",
                "textarea[aria-label*='search']",
                "textarea[aria-label*='Ask']",

                # Alternative input types
                "input[placeholder*='Ask anything']",
                "input[placeholder*='ask anything']",
                "input[data-testid='search-input']",
                "input[data-testid='searchbox']",
                "input[type='text'][placeholder*='search']",

                # Generic selectors
                "textarea:not([style*='display: none'])",
                "input[type='text']:not([style*='display: none'])",

                # Class-based selectors (may change)
                ".search-input",
                ".query-input",
                ".main-search",
                "[role='textbox']"
            ]

            search_input = None
            for selector in search_input_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            search_input = element
                            logger.info(f"Found search input with selector: {selector}")
                            break
                    if search_input:
                        break
                except (NoSuchElementException, StaleElementReferenceException):
                    continue

            if not search_input:
                # Try waiting for any visible input/textarea
                try:
                    search_input = self.wait.until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, "textarea, input[type='text']"))
                    )
                except TimeoutException:
                    raise Exception("Could not find search input field on Perplexity")

            # Click on the search input to focus it
            await self._human_click(search_input)
            await self._random_delay()

            # Clear and enter the query with human-like typing
            await self._human_type(search_input, query)
            await self._random_delay()

            # Handle custom sources if specified
            sources = options.get("sources", [])
            if sources:
                await self._configure_custom_sources(sources)

            # Submit the query (try Enter key first, then look for submit button)
            try:
                search_input.send_keys(Keys.RETURN)
                logger.info("Query submitted with Enter key")
            except Exception as e:
                logger.warning(f"Enter key failed, looking for submit button: {e}")
                await self._find_and_click_submit_button()

            await self._random_delay(1, 2)

            # Determine timeout based on mode
            mode = options.get("mode", "quick_search")
            timeout = (settings.deep_research_timeout if mode == "deep_research"
                      else settings.quick_search_timeout)

            logger.info(f"Waiting for results (mode: {mode}, timeout: {timeout}s)")

            # Wait for results
            result = await self._wait_for_results(timeout, mode)

            return {
                "answer": result.get("answer", ""),
                "sources": result.get("sources", []),
                "mode": mode,
                "timestamp": datetime.utcnow().isoformat(),
                "query": query,
                "processing_time": result.get("processing_time", 0)
            }

        except TimeoutException:
            raise Exception(f"Timeout waiting for Perplexity results after {timeout} seconds")
        except Exception as e:
            logger.error(f"Error executing Perplexity query: {e}")
            raise

    async def _find_and_click_submit_button(self):
        """Find and click the submit button if Enter key doesn't work"""
        submit_selectors = [
            "button[type='submit']",
            "button[aria-label*='search']",
            "button[aria-label*='submit']",
            "button[data-testid='submit']",
            "button[data-testid='search-button']",
            ".search-button",
            ".submit-button",
            "button:contains('Search')",
            "button svg[*|href*='search']",  # SVG search icons
            "button[title*='search']"
        ]

        for selector in submit_selectors:
            try:
                button = self.driver.find_element(By.CSS_SELECTOR, selector)
                if button.is_displayed() and button.is_enabled():
                    await self._human_click(button)
                    logger.info(f"Clicked submit button with selector: {selector}")
                    return
            except (NoSuchElementException, StaleElementReferenceException):
                continue

        logger.warning("Could not find submit button")

    async def _handle_initial_popups(self):
        """Handle any initial popups or modals"""
        try:
            # Common popup selectors
            popup_selectors = [
                "button[aria-label='Close']",
                "button[data-testid='close-button']",
                ".modal-close",
                "[role='dialog'] button",
                "button:contains('Skip')",
                "button:contains('Later')",
                "button:contains('No thanks')"
            ]

            for selector in popup_selectors:
                try:
                    popup = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if popup.is_displayed():
                        popup.click()
                        await asyncio.sleep(1)
                        break
                except (NoSuchElementException, StaleElementReferenceException):
                    continue

        except Exception as e:
            logger.debug(f"No popups to handle or error handling popups: {e}")

    async def _configure_custom_sources(self, sources: List[str]):
        """Configure custom sources for the search"""
        try:
            # Look for sources/focus button or similar
            focus_selectors = [
                "button[data-testid='focus-button']",
                "button:contains('Focus')",
                ".focus-button",
                "[aria-label*='source']"
            ]

            for selector in focus_selectors:
                try:
                    focus_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if focus_button.is_displayed():
                        focus_button.click()
                        await asyncio.sleep(1)

                        # Add custom sources
                        for source in sources:
                            source_input = self.driver.find_element(
                                By.CSS_SELECTOR,
                                "input[placeholder*='domain'], input[placeholder*='source']"
                            )
                            source_input.send_keys(source)
                            source_input.send_keys(Keys.RETURN)
                            await asyncio.sleep(0.5)

                        break

                except (NoSuchElementException, StaleElementReferenceException):
                    continue

        except Exception as e:
            logger.warning(f"Could not configure custom sources: {e}")

    async def _wait_for_results(self, timeout: int, mode: str) -> Dict[str, Any]:
        """Wait for and extract Perplexity results with enhanced detection"""
        start_time = time.time()
        last_progress_log = 0

        logger.info(f"Starting to wait for results (timeout: {timeout}s)")

        while time.time() - start_time < timeout:
            try:
                elapsed = time.time() - start_time

                # Log progress every 30 seconds
                if elapsed - last_progress_log > 30:
                    logger.info(f"Still waiting for results... ({elapsed:.0f}s elapsed)")
                    last_progress_log = elapsed

                # Check if results are ready
                if await self._are_results_ready():
                    processing_time = time.time() - start_time
                    logger.info(f"Results ready after {processing_time:.1f} seconds")
                    result = await self._extract_results()
                    result["processing_time"] = processing_time
                    return result

                # For deep research, check for progress indicators
                if mode == "deep_research":
                    progress = await self._check_deep_research_progress()
                    if progress:
                        logger.info(f"Deep research progress: {progress}")

                # Check for error states
                if await self._check_for_errors():
                    raise Exception("Perplexity returned an error state")

                # Adaptive sleep - shorter intervals initially, longer as time goes on
                if elapsed < 30:
                    await asyncio.sleep(2)
                elif elapsed < 120:
                    await asyncio.sleep(5)
                else:
                    await asyncio.sleep(10)

            except Exception as e:
                logger.error(f"Error while waiting for results: {e}")
                await asyncio.sleep(5)

        raise TimeoutException(f"Results not ready after {timeout} seconds")

    async def _are_results_ready(self) -> bool:
        """Check if results are ready with enhanced selectors"""
        try:
            # Enhanced result detection selectors
            result_selectors = [
                # Main answer containers
                "[data-testid='answer']",
                "[data-testid='copilot-answer']",
                "[data-testid='answer-content']",
                ".answer-content",
                ".result-content",
                ".response-content",
                ".copilot-answer",

                # Content areas
                "div[role='main'] div[class*='answer']",
                "div[role='main'] div[class*='response']",
                "div[role='main'] div[class*='result']",
                "main div[class*='answer']",
                "main div[class*='response']",

                # Text content areas
                ".prose",
                ".markdown",
                ".content",
                "div[class*='prose']",
                "div[class*='markdown']",

                # Generic content selectors
                "div[role='main'] p:not(:empty)",
                "main p:not(:empty)",
                "article p:not(:empty)",

                # Fallback selectors
                "div:contains('Based on')",
                "div:contains('According to')",
                "div:contains('The answer')"
            ]

            for selector in result_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            text = element.text.strip()
                            # Check for meaningful content (not just loading indicators)
                            if (len(text) > 20 and
                                not any(indicator in text.lower() for indicator in
                                       ['loading', 'searching', 'thinking', 'generating', '...'])):
                                logger.debug(f"Found result content with selector: {selector}")
                                return True
                except (NoSuchElementException, StaleElementReferenceException):
                    continue

            # Check for streaming content indicators
            streaming_selectors = [
                ".streaming",
                ".typing",
                "[data-testid='streaming']",
                ".cursor-blink"
            ]

            for selector in streaming_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        logger.debug("Found streaming indicator, content still generating")
                        return False
                except (NoSuchElementException, StaleElementReferenceException):
                    continue

            return False

        except Exception as e:
            logger.debug(f"Error checking if results are ready: {e}")
            return False

    async def _check_for_errors(self) -> bool:
        """Check for error states on the page"""
        try:
            error_selectors = [
                ".error",
                ".error-message",
                "[data-testid='error']",
                ".alert-error",
                ".notification-error",
                "div:contains('Error')",
                "div:contains('Something went wrong')",
                "div:contains('Try again')"
            ]

            for selector in error_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        logger.warning(f"Found error indicator: {element.text}")
                        return True
                except (NoSuchElementException, StaleElementReferenceException):
                    continue

            return False

        except Exception:
            return False

    async def _check_deep_research_progress(self) -> Optional[str]:
        """Check deep research progress"""
        try:
            progress_selectors = [
                ".progress-indicator",
                "[data-testid='progress']",
                ".loading-message",
                ".research-status"
            ]

            for selector in progress_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        return element.text.strip()
                except (NoSuchElementException, StaleElementReferenceException):
                    continue

            return None

        except Exception:
            return None

    async def _extract_results(self) -> Dict[str, Any]:
        """Extract the answer and sources from the page"""
        try:
            # Extract main answer
            answer = await self._extract_answer()

            # Extract sources
            sources = await self._extract_sources()

            return {
                "answer": answer,
                "sources": sources
            }

        except Exception as e:
            logger.error(f"Error extracting results: {e}")
            return {
                "answer": "Error extracting results",
                "sources": []
            }

    async def _extract_answer(self) -> str:
        """Extract the main answer text"""
        try:
            answer_selectors = [
                "[data-testid='answer']",
                ".answer-content",
                ".result-content",
                ".response-content",
                "div[role='main'] div:first-child",
                ".prose p"
            ]

            for selector in answer_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.text.strip():
                            return element.text.strip()
                except (NoSuchElementException, StaleElementReferenceException):
                    continue

            # Fallback: get all visible text
            body = self.driver.find_element(By.TAG_NAME, "body")
            return body.text[:1000] + "..." if len(body.text) > 1000 else body.text

        except Exception as e:
            logger.error(f"Error extracting answer: {e}")
            return "Could not extract answer"

    async def _extract_sources(self) -> List[Dict[str, str]]:
        """Extract source links and titles"""
        try:
            sources = []

            source_selectors = [
                ".source-link",
                ".citation",
                ".reference",
                "a[href*='http']"
            ]

            for selector in source_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            href = element.get_attribute("href")
                            title = element.text.strip() or element.get_attribute("title") or href

                            if href and href.startswith("http"):
                                sources.append({
                                    "title": title,
                                    "url": href
                                })

                                if len(sources) >= 10:  # Limit sources
                                    break

                except (NoSuchElementException, StaleElementReferenceException):
                    continue

                if sources:
                    break

            return sources

        except Exception as e:
            logger.error(f"Error extracting sources: {e}")
            return []
