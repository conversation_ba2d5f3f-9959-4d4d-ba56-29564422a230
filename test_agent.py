#!/usr/bin/env python3
"""
Test script for Perplexity AI Agent
Run this script to test the basic functionality of the agent.
"""

import asyncio
import aiohttp
import json
import time
from typing import Dict, Any


class PerplexityAgentTester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def health_check(self) -> Dict[str, Any]:
        """Check if the agent is healthy"""
        async with self.session.get(f"{self.base_url}/health") as response:
            return await response.json()
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get system statistics"""
        async with self.session.get(f"{self.base_url}/stats") as response:
            return await response.json()
    
    async def submit_query(self, query: str, options: Dict[str, Any] = None) -> str:
        """Submit a query and return job ID"""
        payload = {"query": query}
        if options:
            payload["options"] = options
        
        async with self.session.post(
            f"{self.base_url}/query",
            json=payload,
            headers={"Content-Type": "application/json"}
        ) as response:
            if response.status != 200:
                raise Exception(f"Failed to submit query: {response.status}")
            
            result = await response.json()
            return result["job_id"]
    
    async def get_result(self, job_id: str) -> Dict[str, Any]:
        """Get job result"""
        async with self.session.get(f"{self.base_url}/result/{job_id}") as response:
            return await response.json()
    
    async def get_status(self, job_id: str) -> Dict[str, Any]:
        """Get job status"""
        async with self.session.get(f"{self.base_url}/status/{job_id}") as response:
            return await response.json()
    
    async def wait_for_completion(self, job_id: str, timeout: int = 120) -> Dict[str, Any]:
        """Wait for job completion"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            status = await self.get_status(job_id)
            
            if status["status"] in ["completed", "failed", "cancelled"]:
                return await self.get_result(job_id)
            
            print(f"Job {job_id} status: {status['status']}")
            await asyncio.sleep(5)
        
        raise TimeoutError(f"Job {job_id} did not complete within {timeout} seconds")


async def run_tests():
    """Run comprehensive tests"""
    print("🚀 Starting Perplexity AI Agent Tests")
    print("=" * 50)
    
    async with PerplexityAgentTester() as tester:
        try:
            # Test 1: Health Check
            print("\n1. Testing Health Check...")
            health = await tester.health_check()
            print(f"   Health Status: {health.get('status', 'unknown')}")
            print(f"   Redis: {health.get('redis', 'unknown')}")
            print(f"   Handler: {health.get('handler', 'unknown')}")
            
            if health.get("status") != "healthy":
                print("   ⚠️  System is not healthy, tests may fail")
            else:
                print("   ✅ System is healthy")
            
            # Test 2: System Statistics
            print("\n2. Testing System Statistics...")
            stats = await tester.get_stats()
            print(f"   Pending Jobs: {stats.get('job_stats', {}).get('pending', 0)}")
            print(f"   Active Tabs: {stats.get('active_tabs', 0)}")
            print(f"   Is Running: {stats.get('is_running', False)}")
            print("   ✅ Statistics retrieved successfully")
            
            # Test 3: Quick Search Query
            print("\n3. Testing Quick Search Query...")
            query = "What is the capital of France?"
            print(f"   Query: {query}")
            
            job_id = await tester.submit_query(query, {"mode": "quick_search"})
            print(f"   Job ID: {job_id}")
            print("   ✅ Query submitted successfully")
            
            # Wait for completion
            print("   Waiting for completion...")
            result = await tester.wait_for_completion(job_id, timeout=90)
            
            print(f"   Status: {result.get('status')}")
            if result.get("status") == "completed":
                answer = result.get("result", {}).get("answer", "")
                print(f"   Answer: {answer[:100]}...")
                print("   ✅ Quick search completed successfully")
            else:
                print(f"   ❌ Quick search failed: {result.get('error', 'Unknown error')}")
            
            # Test 4: Deep Research Query (optional, takes longer)
            print("\n4. Testing Deep Research Query (optional)...")
            deep_query = "Latest developments in artificial intelligence 2024"
            print(f"   Query: {deep_query}")
            print("   Note: This test is skipped by default due to long execution time")
            print("   To run deep research test, uncomment the code below")
            
            # Uncomment the following lines to test deep research:
            # job_id = await tester.submit_query(deep_query, {"mode": "deep_research"})
            # print(f"   Job ID: {job_id}")
            # print("   Waiting for completion (this may take 2-5 minutes)...")
            # result = await tester.wait_for_completion(job_id, timeout=360)
            # print(f"   Status: {result.get('status')}")
            
            print("\n🎉 All tests completed!")
            print("\n📋 Test Summary:")
            print("   ✅ Health check passed")
            print("   ✅ Statistics retrieval passed")
            print("   ✅ Quick search query passed")
            print("   ⏭️  Deep research test skipped")
            
            print("\n🔧 Next Steps:")
            print("   1. Connect to VNC viewer at localhost:5900 to watch automation")
            print("   2. Visit http://localhost:8000/debug for web-based debugging")
            print("   3. Check API documentation at http://localhost:8000/docs")
            print("   4. Submit your own queries using the API")
            
        except Exception as e:
            print(f"\n❌ Test failed with error: {e}")
            print("\n🔍 Troubleshooting:")
            print("   1. Ensure all Docker containers are running: docker-compose ps")
            print("   2. Check logs: docker-compose logs perplexity-agent")
            print("   3. Verify services are accessible: curl http://localhost:8000/health")
            print("   4. Wait a few minutes for services to fully initialize")


if __name__ == "__main__":
    asyncio.run(run_tests())
