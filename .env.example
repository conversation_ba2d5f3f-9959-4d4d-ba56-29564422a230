# Redis Configuration
REDIS_URL=redis://redis:6379
REDIS_DB=0
REDIS_PASSWORD=

# Selenium Configuration
SELENIUM_HUB_URL=http://selenium-hub:4444/wd/hub
SELENIUM_TIMEOUT=30
PAGE_LOAD_TIMEOUT=60
IMPLICIT_WAIT=10

# Perplexity Configuration
PERPLEXITY_BASE_URL=https://www.perplexity.ai
DEEP_RESEARCH_TIMEOUT=600
QUICK_SEARCH_TIMEOUT=600
PAGE_INTERACTION_TIMEOUT=30

# Perplexity Authentication (Optional)
ENABLE_PERPLEXITY_AUTH=false
PERPLEXITY_EMAIL=<EMAIL>
MANUAL_EMAIL_VERIFICATION=true

# Job Configuration
JOB_EXPIRY_SECONDS=3600
MAX_CONCURRENT_JOBS=10
JOB_RETRY_ATTEMPTS=3
JOB_RETRY_DELAY=5

# Application Configuration
DEBUG_MODE=true
LOG_LEVEL=INFO
API_TITLE=Perplexity AI Agent
API_VERSION=1.0.0

# Browser Configuration
BROWSER_WINDOW_WIDTH=1920
BROWSER_WINDOW_HEIGHT=1080
HEADLESS_MODE=false

# Anti-Detection Configuration
ENABLE_STEALTH_MODE=true
RANDOM_USER_AGENT=true
SIMULATE_HUMAN_BEHAVIOR=true
RANDOM_DELAYS_MIN=1.0
RANDOM_DELAYS_MAX=3.0
MOUSE_MOVEMENT_SIMULATION=true

# Browser Fingerprinting Countermeasures
DISABLE_WEBRTC=true
DISABLE_WEBGL=false
DISABLE_PLUGINS=true
DISABLE_EXTENSIONS=true
DISABLE_IMAGES=false
CUSTOM_VIEWPORT_SIZES=true
