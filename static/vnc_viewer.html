<!DOCTYPE html>
<html>
<head>
    <title>VNC Browser Debug Viewer</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2em;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .content {
            padding: 20px;
        }
        
        .info-panel {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .info-panel h3 {
            margin-top: 0;
            color: #0c5460;
        }
        
        .connection-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .connection-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
        }
        
        .connection-card h4 {
            margin-top: 0;
            color: #495057;
        }
        
        .connection-string {
            background: #343a40;
            color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            word-break: break-all;
        }
        
        .stats-panel {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .stat-item {
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .instructions {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 20px;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #0c5460;
        }
        
        .instructions ol {
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
        
        .refresh-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .refresh-btn:hover {
            background: #0056b3;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-running {
            background-color: #28a745;
        }
        
        .status-stopped {
            background-color: #dc3545;
        }
        
        .status-unknown {
            background-color: #ffc107;
        }
        
        @media (max-width: 768px) {
            .connection-info {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Perplexity AI Agent Debug Viewer</h1>
            <p>Real-time browser automation monitoring and debugging</p>
        </div>
        
        <div class="content">
            <div class="info-panel">
                <h3>🖥️ VNC Connection Information</h3>
                <div class="connection-info">
                    <div class="connection-card">
                        <h4>Direct VNC Connection</h4>
                        <div class="connection-string">vnc://localhost:5900</div>
                        <p><small>Click the connection string above to open in your default VNC client (macOS/Linux)</small></p>
                    </div>
                    
                    <div class="connection-card">
                        <h4>Manual Connection</h4>
                        <div class="connection-string">
                            Host: localhost<br>
                            Port: 5900<br>
                            Password: (none)
                        </div>
                        <p><small>Use these details in your VNC client application</small></p>
                    </div>
                </div>
            </div>
            
            <div class="stats-panel">
                <h3>📊 System Statistics <button class="refresh-btn" onclick="loadStats()">Refresh</button></h3>
                <div id="stats-content">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value" id="pending-jobs">-</div>
                            <div class="stat-label">Pending Jobs</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="active-tabs">-</div>
                            <div class="stat-label">Active Browser Tabs</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="completed-jobs">-</div>
                            <div class="stat-label">Completed Jobs</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="system-status">
                                <span class="status-indicator status-unknown"></span>
                                <span id="status-text">Loading...</span>
                            </div>
                            <div class="stat-label">System Status</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="instructions">
                <h3>🚀 Getting Started</h3>
                <ol>
                    <li><strong>Install a VNC Client:</strong>
                        <ul>
                            <li><strong>macOS:</strong> Use built-in Screen Sharing or download VNC Viewer</li>
                            <li><strong>Windows:</strong> Download TightVNC, RealVNC, or VNC Viewer</li>
                            <li><strong>Linux:</strong> Install Remmina, Vinagre, or use vncviewer command</li>
                        </ul>
                    </li>
                    <li><strong>Connect to VNC:</strong> Use the connection details above to connect to localhost:5900</li>
                    <li><strong>Submit a Query:</strong> Use the API endpoint <code>POST /query</code> to submit a test query</li>
                    <li><strong>Watch the Automation:</strong> Observe the browser automation in real-time through VNC</li>
                    <li><strong>Debug Issues:</strong> Use the VNC viewer to see exactly what's happening during automation</li>
                </ol>
                
                <h4>💡 Pro Tips:</h4>
                <ul>
                    <li>The browser runs in non-headless mode specifically for debugging purposes</li>
                    <li>Each query opens in a new browser tab for concurrent processing</li>
                    <li>Screenshots are automatically captured on errors for debugging</li>
                    <li>Use the stats above to monitor system performance</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        async function loadStats() {
            try {
                const response = await fetch('/stats');
                const stats = await response.json();
                
                // Update stats display
                document.getElementById('pending-jobs').textContent = stats.job_stats?.pending || 0;
                document.getElementById('active-tabs').textContent = stats.active_tabs || 0;
                document.getElementById('completed-jobs').textContent = stats.job_stats?.completed || 0;
                
                // Update system status
                const statusIndicator = document.querySelector('.status-indicator');
                const statusText = document.getElementById('status-text');
                
                if (stats.is_running) {
                    statusIndicator.className = 'status-indicator status-running';
                    statusText.textContent = 'Running';
                } else {
                    statusIndicator.className = 'status-indicator status-stopped';
                    statusText.textContent = 'Stopped';
                }
                
                console.log('Stats updated:', stats);
                
            } catch (error) {
                console.error('Error loading stats:', error);
                document.getElementById('status-text').textContent = 'Error';
                document.querySelector('.status-indicator').className = 'status-indicator status-unknown';
            }
        }
        
        // Load stats on page load
        loadStats();
        
        // Auto-refresh stats every 10 seconds
        setInterval(loadStats, 10000);
        
        // Make VNC connection string clickable on macOS
        document.addEventListener('DOMContentLoaded', function() {
            const connectionStrings = document.querySelectorAll('.connection-string');
            connectionStrings.forEach(function(element) {
                if (element.textContent.startsWith('vnc://')) {
                    element.style.cursor = 'pointer';
                    element.addEventListener('click', function() {
                        window.open(element.textContent, '_blank');
                    });
                }
            });
        });
    </script>
</body>
</html>
