<!DOCTYPE html>
<html>
<head>
    <title>VNC Browser Debug Viewer</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2em;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .content {
            padding: 20px;
        }
        
        .info-panel {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .info-panel h3 {
            margin-top: 0;
            color: #0c5460;
        }
        
        .connection-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .connection-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
        }
        
        .connection-card h4 {
            margin-top: 0;
            color: #495057;
        }
        
        .connection-string {
            background: #343a40;
            color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            word-break: break-all;
        }
        
        .stats-panel {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .stat-item {
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .instructions {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 20px;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #0c5460;
        }
        
        .instructions ol {
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
        
        .refresh-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .refresh-btn:hover {
            background: #0056b3;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-running {
            background-color: #28a745;
        }
        
        .status-stopped {
            background-color: #dc3545;
        }
        
        .status-unknown {
            background-color: #ffc107;
        }
        
        @media (max-width: 768px) {
            .connection-info {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Perplexity AI Agent Debug Viewer</h1>
            <p>Real-time browser automation monitoring and debugging</p>
        </div>
        
        <div class="content">
            <div class="info-panel">
                <h3>🖥️ Browser Debug Connection</h3>
                <div class="connection-info">
                    <div class="connection-card">
                        <h4>noVNC Web Interface (Recommended)</h4>
                        <div class="connection-string">http://localhost:7900</div>
                        <p><small><a href="http://localhost:7900" target="_blank" style="color: #007bff;">Click here to open the browser viewer</a></small></p>
                        <p><small>✅ No additional software required - works in any web browser</small></p>
                    </div>

                    <div class="connection-card">
                        <h4>Embedded Viewer</h4>
                        <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 10px;">
                            <iframe src="http://localhost:7900" style="width: 100%; height: 300px; border: 1px solid #ccc; border-radius: 4px;"></iframe>
                        </div>
                        <p><small>Browser automation viewer embedded above</small></p>
                    </div>
                </div>
            </div>
            
            <div class="stats-panel">
                <h3>📊 System Statistics <button class="refresh-btn" onclick="loadStats()">Refresh</button></h3>
                <div id="stats-content">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value" id="pending-jobs">-</div>
                            <div class="stat-label">Pending Jobs</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="active-tabs">-</div>
                            <div class="stat-label">Active Browser Tabs</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="completed-jobs">-</div>
                            <div class="stat-label">Completed Jobs</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="system-status">
                                <span class="status-indicator status-unknown"></span>
                                <span id="status-text">Loading...</span>
                            </div>
                            <div class="stat-label">System Status</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="instructions">
                <h3>🚀 Getting Started</h3>
                <ol>
                    <li><strong>Access Browser Viewer:</strong> Click the link above or visit <code>http://localhost:7900</code></li>
                    <li><strong>Submit a Query:</strong> Use the API endpoint <code>POST /query</code> to submit a test query</li>
                    <li><strong>Watch the Automation:</strong> Observe the browser automation in real-time through the web viewer</li>
                    <li><strong>Debug Issues:</strong> Use the viewer to see exactly what's happening during automation</li>
                    <li><strong>Interact if Needed:</strong> You can click and interact with the browser through the viewer</li>
                </ol>

                <h4>💡 Pro Tips:</h4>
                <ul>
                    <li>✅ No VNC client installation required - everything works in your web browser</li>
                    <li>✅ The browser runs in non-headless mode specifically for debugging purposes</li>
                    <li>✅ Each query opens in a new browser tab for concurrent processing</li>
                    <li>✅ Screenshots are automatically captured on errors for debugging</li>
                    <li>✅ Use the stats above to monitor system performance</li>
                    <li>✅ Full screen mode available in the noVNC interface</li>
                </ul>

                <h4>🔧 Quick Test Commands:</h4>
                <div style="background: #343a40; color: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; margin-top: 10px;">
# Submit a test query<br>
curl -X POST http://localhost:8000/query \<br>
&nbsp;&nbsp;-H "Content-Type: application/json" \<br>
&nbsp;&nbsp;-d '{"query": "What is the weather like today?"}'<br><br>
# Run the test script<br>
python test_agent.py
                </div>
            </div>
        </div>
    </div>
    
    <script>
        async function loadStats() {
            try {
                const response = await fetch('/stats');
                const stats = await response.json();
                
                // Update stats display
                document.getElementById('pending-jobs').textContent = stats.job_stats?.pending || 0;
                document.getElementById('active-tabs').textContent = stats.active_tabs || 0;
                document.getElementById('completed-jobs').textContent = stats.job_stats?.completed || 0;
                
                // Update system status
                const statusIndicator = document.querySelector('.status-indicator');
                const statusText = document.getElementById('status-text');
                
                if (stats.is_running) {
                    statusIndicator.className = 'status-indicator status-running';
                    statusText.textContent = 'Running';
                } else {
                    statusIndicator.className = 'status-indicator status-stopped';
                    statusText.textContent = 'Stopped';
                }
                
                console.log('Stats updated:', stats);
                
            } catch (error) {
                console.error('Error loading stats:', error);
                document.getElementById('status-text').textContent = 'Error';
                document.querySelector('.status-indicator').className = 'status-indicator status-unknown';
            }
        }
        
        // Load stats on page load
        loadStats();
        
        // Auto-refresh stats every 10 seconds
        setInterval(loadStats, 10000);
        
        // Make VNC connection string clickable on macOS
        document.addEventListener('DOMContentLoaded', function() {
            const connectionStrings = document.querySelectorAll('.connection-string');
            connectionStrings.forEach(function(element) {
                if (element.textContent.startsWith('vnc://')) {
                    element.style.cursor = 'pointer';
                    element.addEventListener('click', function() {
                        window.open(element.textContent, '_blank');
                    });
                }
            });
        });
    </script>
</body>
</html>
